#!/usr/bin/env python3
"""
CATEGORY-BALANCED Progressive Vintern Training for L4 GPU Server
- 700k samples dataset (FINAL_ULTIMATE_COMPLETE_DATASET)
- L4 GPU optimized configuration (24GB VRAM)
- Checkpoint/Resume functionality
- Enhanced model saving and versioning
- Robust error handling for long training sessions
"""

import torch
import json
import logging
import time
import os
from PIL import Image
import requests
from io import BytesIO
import torchvision.transforms as transforms
from transformers import AutoTokenizer, AutoModel, get_linear_schedule_with_warmup
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import gc
import random
from collections import defaultdict, Counter
import pandas as pd
from datetime import datetime
import hashlib
import pickle

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vintern_l4_server_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CategoryBalancedDataset(Dataset):
    """Category-balanced dataset với cache mechanism cho server training"""
    
    def __init__(self, data_df, tokenizer, difficulty_level=1, is_training=True, cache_dir="image_cache"):
        self.tokenizer = tokenizer
        self.difficulty_level = difficulty_level
        self.is_training = is_training
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Organize data by category
        self.category_data = defaultdict(list)
        self.categories = []
        
        for _, row in data_df.iterrows():
            category = row['final_ultimate_category']
            self.category_data[category].append({
                'food_name': row['food_name'],
                'category': category,
                'image_url': row['URL'],
                'normalized_name': row.get('normalized_name', row['food_name'].lower().strip()),
                'row_id': row.name  # For cache key
            })
        
        self.categories = list(self.category_data.keys())
        
        # Progressive filtering by difficulty
        self.filtered_categories = self._filter_categories_by_difficulty()
        
        # Calculate category complexity scores
        self.category_complexity = self._calculate_category_complexity()
        
        # L4 optimized transforms
        self.transform = transforms.Compose([
            transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        logger.info(f"Dataset Level {difficulty_level}: {len(self.filtered_categories)} categories, "
                   f"{sum(len(self.category_data[cat]) for cat in self.filtered_categories)} samples")
    
    def _calculate_category_complexity(self):
        """Calculate complexity score for each category"""
        complexity = {}
        
        for category in self.categories:
            score = 0
            
            # Name complexity (longer = harder)
            avg_name_length = np.mean([len(item['food_name'].split()) 
                                     for item in self.category_data[category]])
            score += min(avg_name_length, 5) * 10
            
            # Category name complexity
            cat_words = len(category.split())
            score += cat_words * 5
            
            # Sample count complexity (more samples = more diverse, harder)
            sample_count = len(self.category_data[category])
            score += min(sample_count / 10, 20)
            
            # Normalize to 0-100
            complexity[category] = min(score, 100)
        
        return complexity
    
    def _filter_categories_by_difficulty(self):
        """Filter categories based on difficulty level"""
        if not hasattr(self, 'category_complexity'):
            # Initial calculation
            temp_complexity = {}
            for category in self.categories:
                score = len(category.split()) * 10 + np.random.randint(0, 30)
                temp_complexity[category] = score
            
            sorted_categories = sorted(temp_complexity.keys(), key=lambda x: temp_complexity[x])
        else:
            sorted_categories = sorted(self.categories, key=lambda x: self.category_complexity[x])
        
        if self.difficulty_level == 1:
            # Easy: first 1/3 of categories
            return sorted_categories[:len(sorted_categories)//3]
        elif self.difficulty_level == 2:
            # Medium: first 2/3 of categories  
            return sorted_categories[:2*len(sorted_categories)//3]
        else:
            # Hard: all categories
            return sorted_categories
    
    def update_difficulty(self, new_level):
        """Update difficulty level"""
        self.difficulty_level = new_level
        self.filtered_categories = self._filter_categories_by_difficulty()
        logger.info(f"Updated to level {new_level}: {len(self.filtered_categories)} categories")
    
    def __len__(self):
        """Total samples in filtered categories"""
        return sum(len(self.category_data[cat]) for cat in self.filtered_categories)
    
    def __getitem__(self, idx):
        """Get item by global index với caching"""
        # Convert global index to (category, local_index)
        current_idx = 0
        for category in self.filtered_categories:
            cat_size = len(self.category_data[category])
            if current_idx + cat_size > idx:
                local_idx = idx - current_idx
                item = self.category_data[category][local_idx]
                break
            current_idx += cat_size
        else:
            # Fallback
            item = self.category_data[self.filtered_categories[0]][0]
        
        # Load and process image với cache
        tensor = self._load_image_cached(item)
        
        # Progressive instructions
        instruction = self._get_progressive_instruction(item, self.difficulty_level)
        response = self._get_target_response(item, self.difficulty_level)
        
        return {
            'image_tensor': tensor,
            'instruction': instruction,
            'response': response,
            'food_name': item['food_name'],
            'category': item['category'],
            'difficulty': self.difficulty_level
        }
    
    def _get_cache_path(self, item):
        """Generate cache path for image"""
        cache_key = hashlib.md5(
            f"{item['image_url']}_{item['row_id']}".encode()
        ).hexdigest()
        return self.cache_dir / f"{cache_key}.pt"
    
    def _load_image_cached(self, item):
        """Load image với cache mechanism"""
        cache_path = self._get_cache_path(item)
        
        # Try to load from cache first
        if cache_path.exists():
            try:
                return torch.load(cache_path, map_location='cpu')
            except Exception as e:
                logger.warning(f"Cache load failed: {e}")
                cache_path.unlink(missing_ok=True)
        
        # Load from URL and cache
        tensor = self._load_image(item['image_url'])
        
        # Save to cache
        try:
            torch.save(tensor, cache_path)
        except Exception as e:
            logger.warning(f"Cache save failed: {e}")
        
        return tensor
    
    def _load_image(self, image_url):
        """Load and process image với enhanced error handling cho server"""
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'image/*,*/*;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                }

                response = requests.get(
                    image_url,
                    headers=headers,
                    timeout=20,  # Tăng timeout cho server
                    verify=False,
                    stream=True  # Stream để handle large images
                )
                response.raise_for_status()

                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if not any(img_type in content_type for img_type in ['image/', 'jpeg', 'png', 'jpg', 'webp']):
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        raise ValueError(f"Invalid content type: {content_type}")

                image = Image.open(BytesIO(response.content))
                if image.mode != 'RGB':
                    image = image.convert('RGB')

                # EXIF orientation handling
                if hasattr(image, '_getexif') and image._getexif() is not None:
                    exif = image._getexif()
                    if exif is not None and 274 in exif:
                        orientation = exif[274]
                        if orientation == 3:
                            image = image.rotate(180, expand=True)
                        elif orientation == 6:
                            image = image.rotate(270, expand=True)
                        elif orientation == 8:
                            image = image.rotate(90, expand=True)

                return self.transform(image).unsqueeze(0)

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Image load attempt {attempt + 1} failed: {e}, retrying...")
                    time.sleep(retry_delay * (attempt + 1))
                else:
                    logger.warning(f"Image load failed after {max_retries} attempts: {e}")
                    return torch.zeros(1, 3, 448, 448)
    
    def _get_progressive_instruction(self, item, level):
        """Generate progressive instructions"""
        if level == 1:
            return "What Vietnamese food is shown in this image?"
        elif level == 2:
            return f"Identify this Vietnamese dish and describe its category."
        else:
            return f"Analyze this Vietnamese food image. Provide the dish name, category, and key characteristics."
    
    def _get_target_response(self, item, level):
        """Generate target responses"""
        if level == 1:
            return f"This is {item['food_name']}."
        elif level == 2:
            return f"This is {item['food_name']}, which belongs to the {item['category']} category."
        else:
            return f"This is {item['food_name']}, a Vietnamese dish from the {item['category']} category. It represents traditional Vietnamese cuisine with distinctive characteristics."

class FixedBalancedBatchSampler:
    """FIXED: L4 GPU optimized batch sampler with finite epochs (từ local code)"""

    def __init__(self, dataset, batch_size, shuffle=True, max_batches_per_epoch=None):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.categories = dataset.filtered_categories

        # L4 GPU có 24GB VRAM nên có thể handle batch lớn hơn
        self.effective_batch_size = min(batch_size, len(self.categories))

        # Calculate reasonable max batches per epoch cho 700k dataset
        total_samples = sum(len(dataset.category_data[cat]) for cat in self.categories)
        min_samples_per_category = min(len(dataset.category_data[cat]) for cat in self.categories) if self.categories else 1

        # Cho 700k samples: tăng max_batches để đảm bảo coverage tốt
        if max_batches_per_epoch is None:
            # Tính toán dựa trên total samples và effective batch size
            base_batches = total_samples // self.effective_batch_size
            # Giới hạn hợp lý: không quá 2000 batches/epoch để tránh quá dài
            self.max_batches_per_epoch = min(base_batches, max(500, min_samples_per_category * 3))
        else:
            self.max_batches_per_epoch = max_batches_per_epoch

        logger.info(f"FIXED L4 Sampler: {len(self.categories)} categories, "
                   f"effective batch size: {self.effective_batch_size}, "
                   f"max batches per epoch: {self.max_batches_per_epoch}, "
                   f"total samples: {total_samples}")

    def __iter__(self):
        """Generate finite balanced batches per epoch (FIXED từ local)"""
        # Create category indices
        category_indices = {}
        global_idx = 0

        for category in self.categories:
            cat_samples = len(self.dataset.category_data[category])
            category_indices[category] = list(range(global_idx, global_idx + cat_samples))
            global_idx += cat_samples

        # Shuffle indices within each category
        if self.shuffle:
            for category in category_indices:
                random.shuffle(category_indices[category])

        # Create category iterators
        category_iterators = {cat: iter(indices) for cat, indices in category_indices.items()}

        # Generate exactly max_batches_per_epoch batches (FIXED)
        batches_generated = 0

        while batches_generated < self.max_batches_per_epoch:
            batch = []
            selected_categories = (random.sample(self.categories, self.effective_batch_size)
                                 if self.shuffle else self.categories[:self.effective_batch_size])

            for category in selected_categories:
                try:
                    idx = next(category_iterators[category])
                    batch.append(idx)
                except StopIteration:
                    # Restart iterator for this category
                    if self.shuffle:
                        random.shuffle(category_indices[category])
                    category_iterators[category] = iter(category_indices[category])
                        idx = next(category_iterators[category])
                        batch.append(idx)
                    except StopIteration:
                        # If category is empty, don't break if we have some samples
                        if len(batch) > 0:
                            continue
                        break

            if len(batch) > 0:
                yield batch
                batches_generated += 1
            else:
                break

    def __len__(self):
        """Return fixed number of batches per epoch (FIXED)"""
        return self.max_batches_per_epoch

def balanced_collate_fn(batch):
    """L4 optimized collate function"""
    images = torch.cat([item['image_tensor'] for item in batch], dim=0)
    instructions = [item['instruction'] for item in batch]
    responses = [item['response'] for item in batch]
    food_names = [item['food_name'] for item in batch]
    categories = [item['category'] for item in batch]
    difficulties = [item['difficulty'] for item in batch]
    
    # Log category diversity in batch
    unique_categories = len(set(categories))
    if len(batch) > 0 and random.random() < 0.05:  # Log 5% of batches
        logger.debug(f"Batch diversity: {unique_categories}/{len(batch)} unique categories")
    
    return {
        'images': images,
        'instructions': instructions,
        'responses': responses,
        'food_names': food_names,
        'categories': categories,
        'difficulties': difficulties
    }

def enhanced_compute_loss(model, tokenizer, batch, device):
    """Enhanced loss with L4 GPU optimization"""
    images = batch['images'].to(device, dtype=torch.bfloat16)  # L4 supports bfloat16
    instructions = batch['instructions']
    food_names = batch['food_names']
    categories = batch['categories']
    
    batch_size = images.size(0)
    total_loss = 0.0
    valid_samples = 0
    
    # Category diversity bonus
    unique_categories = len(set(categories))
    diversity_bonus = min(unique_categories / batch_size, 1.0) * 0.1
    
    for i in range(batch_size):
        try:
            single_image = images[i:i+1]
            
            generated = model.chat(
                tokenizer,
                single_image,
                instructions[i],
                generation_config=dict(
                    max_new_tokens=80,  # Đồng bộ với local
                    do_sample=False,
                    temperature=0.1,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.eos_token_id
                )
            )
            
            generated_lower = generated.lower()
            food_name_lower = food_names[i].lower()
            category_lower = categories[i].lower()
            
            # Multi-criteria scoring
            score = 0.0
            
            # Food name matching (50% weight)
            food_words = [word for word in food_name_lower.split() if len(word) > 2]
            if food_words:
                name_score = sum(1 for word in food_words if word in generated_lower) / len(food_words)
                score += name_score * 0.5
            
            # Category matching (30% weight)
            category_words = [word for word in category_lower.split() if len(word) > 2]
            if category_words:
                cat_score = sum(1 for word in category_words if word in generated_lower) / len(category_words)
                score += cat_score * 0.3
            
            # Vietnamese context (20% weight) - đồng bộ với local
            context_keywords = ['vietnamese', 'dish', 'food', 'cuisine']  # Bỏ 'traditional' để đồng bộ
            context_matches = sum(1 for keyword in context_keywords if keyword in generated_lower)
            context_score = min(context_matches / len(context_keywords), 1.0)
            score += context_score * 0.2

            # Apply diversity bonus
            score += diversity_bonus

            # Convert to loss - đồng bộ với local
            loss = max(0.05, 1.0 - score)  # Đồng bộ minimum loss với local
            total_loss += loss
            valid_samples += 1

        except Exception as e:
            # Enhanced error handling cho server
            if "CUDA out of memory" in str(e):
                logger.error(f"CUDA OOM at sample {i}, clearing cache...")
                torch.cuda.empty_cache()
                gc.collect()
            else:
                logger.warning(f"Sample {i} failed: {str(e)[:100]}")
            total_loss += 1.0
            valid_samples += 1
    
    avg_loss = total_loss / max(valid_samples, 1)
    return torch.tensor(avg_loss, requires_grad=True, device=device, dtype=torch.bfloat16)

class ServerL4ProgressiveTrainer:
    """L4 GPU Server Trainer với checkpoint functionality"""
    
    def __init__(self, model_path="Vintern-1B-v3_5", output_dir="vintern_l4_server_training"):
        self.model_path = model_path
        self.output_dir = output_dir
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.checkpoint_dir = Path(output_dir) / "checkpoints"
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"L4 Server Trainer - Model: {model_path}, Device: {self.device}")
        Path(output_dir).mkdir(exist_ok=True)
        self.load_model()
    
    def load_model(self):
        """Load Vintern model với L4 optimization"""
        logger.info("Loading Vintern v3.5 for L4 GPU...")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path, trust_remote_code=True)
        self.model = AutoModel.from_pretrained(
            self.model_path,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16,  # L4 optimized dtype
            device_map="auto",
            low_cpu_mem_usage=True,
            attn_implementation="flash_attention_2" if hasattr(torch.nn, 'functional') else None
        )
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        logger.info("Model loaded successfully on L4 GPU")
    
    def get_l4_optimal_batch_size(self, num_categories):
        """L4 GPU optimized batch size - 24GB VRAM"""
        # L4 có 24GB VRAM, có thể handle batch size lớn
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU Memory: {gpu_memory:.1f}GB")
        
        if gpu_memory >= 20:  # L4 24GB
            return min(48, num_categories)  # Lớn hơn để tận dụng VRAM
        elif gpu_memory >= 16:
            return min(32, num_categories)
        else:
            return min(24, num_categories)
    
    def save_checkpoint(self, epoch, level, model, optimizer, scheduler, 
                       train_losses, val_losses, best_loss):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'level': level,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'train_losses': train_losses,
            'val_losses': val_losses,
            'best_loss': best_loss,
            'timestamp': datetime.now().isoformat(),
            'device': str(self.device)
        }
        
        checkpoint_path = self.checkpoint_dir / f"checkpoint_level_{level}_epoch_{epoch}.pt"
        torch.save(checkpoint, checkpoint_path)
        
        # Keep only last 3 checkpoints per level
        level_checkpoints = sorted(self.checkpoint_dir.glob(f"checkpoint_level_{level}_*.pt"))
        if len(level_checkpoints) > 3:
            for old_checkpoint in level_checkpoints[:-3]:
                old_checkpoint.unlink()
        
        logger.info(f"Checkpoint saved: {checkpoint_path}")
        return checkpoint_path
    
    def load_checkpoint(self, checkpoint_path=None, level=None):
        """Load training checkpoint"""
        if checkpoint_path is None:
            # Find latest checkpoint for level
            if level:
                pattern = f"checkpoint_level_{level}_*.pt"
            else:
                pattern = "checkpoint_*.pt"
            
            checkpoints = sorted(self.checkpoint_dir.glob(pattern))
            if not checkpoints:
                logger.info("No checkpoint found")
                return None
            checkpoint_path = checkpoints[-1]
        
        logger.info(f"Loading checkpoint: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        return checkpoint
    
    def load_and_prepare_data(self):
        """Load full 700k dataset"""
        logger.info("Loading FINAL_ULTIMATE_COMPLETE_DATASET (700k samples)...")

        # Try different possible file paths
        possible_paths = [
            "FINAL_ULTIMATE_COMPLETE_DATASET.csv",
            "FINAL_ULTIMATE_COMPLETE_DATASET.json",
            "data/FINAL_ULTIMATE_COMPLETE_DATASET.csv",
            "dataset/FINAL_ULTIMATE_COMPLETE_DATASET.csv",
            "/content/FINAL_ULTIMATE_COMPLETE_DATASET.csv"  # Colab/server path
        ]
        
        df = None
        for path in possible_paths:
            try:
                if Path(path).exists():
                    if path.endswith('.csv'):
                        df = pd.read_csv(path)
                    elif path.endswith('.json'):
                        df = pd.read_json(path)
                    logger.info(f"Loaded data from: {path}")
                    break
            except Exception as e:
                logger.warning(f"Failed to load {path}: {e}")
                continue
        
        if df is None:
            raise FileNotFoundError("Could not find FINAL_ULTIMATE_COMPLETE_DATASET file")
        
        # Verify expected columns
        required_cols = ['food_name', 'final_ultimate_category', 'URL']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"Required column '{col}' not found in dataset")
        
        # Add normalized_name if not present
        if 'normalized_name' not in df.columns:
            df['normalized_name'] = df['food_name'].str.lower().str.strip()
        
        logger.info(f"Dataset loaded: {len(df)} samples, {df['final_ultimate_category'].nunique()} categories")

        # Log dataset statistics
        category_counts = df['final_ultimate_category'].value_counts()
        logger.info(f"Category distribution - Min: {category_counts.min()}, "
                   f"Max: {category_counts.max()}, Mean: {category_counts.mean():.1f}")
        logger.info(f"Top 5 categories: {category_counts.head().to_dict()}")

        # URL validation sampling cho server (validate 1% để ước tính)
        sample_size = min(1000, len(df) // 100)  # 1% hoặc tối đa 1000 URLs
        if sample_size > 0:
            logger.info(f"Validating {sample_size} sample URLs...")
            valid_urls = self._validate_sample_urls(df.sample(sample_size)['URL'].tolist())
            logger.info(f"URL validation: {valid_urls}/{sample_size} ({valid_urls/sample_size*100:.1f}%) valid")

        return df

    def _validate_sample_urls(self, urls):
        """Validate sample URLs để ước tính chất lượng dataset"""
        valid_count = 0

        for url in urls:
            try:
                response = requests.head(url, timeout=10, verify=False)
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    if any(img_type in content_type for img_type in ['image/', 'jpeg', 'png', 'jpg']):
                        valid_count += 1
            except:
                continue

        return valid_count
    
    def split_data(self, df):
        """Split data 70/15/15 maintaining category balance"""
        logger.info("Creating balanced 70/15/15 split...")
        categories = df['final_ultimate_category'].unique()
        
        train_dfs = []
        val_dfs = []
        test_dfs = []
        
        for category in categories:
            cat_df = df[df['final_ultimate_category'] == category].copy()
            n = len(cat_df)
            
            if n < 3:  # Skip categories with too few samples
                continue
            
            # Shuffle
            cat_df = cat_df.sample(frac=1, random_state=42).reset_index(drop=True)
            
            # Split 70/15/15
            train_end = max(1, int(n * 0.7))
            val_end = max(train_end + 1, int(n * 0.85))
            
            train_dfs.append(cat_df.iloc[:train_end])
            val_dfs.append(cat_df.iloc[train_end:val_end])
            test_dfs.append(cat_df.iloc[val_end:])
        
        train_df = pd.concat(train_dfs, ignore_index=True)
        val_df = pd.concat(val_dfs, ignore_index=True)
        test_df = pd.concat(test_dfs, ignore_index=True)
        
        logger.info(f"Balanced split - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
        return train_df, val_df, test_df
    
    def create_balanced_dataloaders(self, train_df, val_df, batch_size, difficulty_level=1):
        """Create FIXED L4 optimized dataloaders với finite epochs"""
        train_dataset = CategoryBalancedDataset(train_df, self.tokenizer, difficulty_level, True)
        val_dataset = CategoryBalancedDataset(val_df, self.tokenizer, difficulty_level, False)

        # Calculate reasonable batch limits cho 700k dataset
        train_categories = len(train_dataset.filtered_categories)
        val_categories = len(val_dataset.filtered_categories)

        # Tối ưu cho 700k: tăng max_batches nhưng vẫn hợp lý
        max_train_batches = min(1000, train_categories * 4)  # Max 1000 batches/epoch
        max_val_batches = min(200, val_categories * 2)       # Max 200 val batches

        # FIXED balanced samplers với finite epochs
        train_sampler = FixedBalancedBatchSampler(
            train_dataset, batch_size, shuffle=True, max_batches_per_epoch=max_train_batches
        )
        val_sampler = FixedBalancedBatchSampler(
            val_dataset, batch_size//2, shuffle=False, max_batches_per_epoch=max_val_batches
        )

        train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_sampler,
            num_workers=4,  # L4 server có nhiều CPU cores
            pin_memory=True,
            collate_fn=balanced_collate_fn,
            persistent_workers=True
        )

        val_loader = DataLoader(
            val_dataset,
            batch_sampler=val_sampler,
            num_workers=2,
            pin_memory=True,
            collate_fn=balanced_collate_fn,
            persistent_workers=True
        )

        logger.info(f"FIXED DataLoaders created - Train batches: {len(train_loader)}, Val batches: {len(val_loader)}")

        return train_dataset, val_dataset, train_loader, val_loader
    
    def save_model_versions(self, model, tokenizer, suffix=""):
        """Save model với versioning"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save current version
        current_path = Path(self.output_dir) / f"model_{suffix}_{timestamp}"
        model.save_pretrained(current_path)
        tokenizer.save_pretrained(current_path)
        
        # Create/update symbolic links for easy access
        if suffix:
            latest_path = Path(self.output_dir) / f"latest_{suffix}"
            if latest_path.exists() or latest_path.is_symlink():
                latest_path.unlink()
            latest_path.symlink_to(current_path.name)
        
        logger.info(f"Model saved: {current_path}")
        return current_path
    
    def progressive_train_with_checkpoints(self, num_epochs_per_level=3, resume_from=None):
        """Main training function với checkpoint support"""
        logger.info("Starting L4 Server Progressive Training với 700k dataset...")
        start_time = time.time()
        
        # Load and prepare data
        df = self.load_and_prepare_data()
        train_df, val_df, test_df = self.split_data(df)
        
        num_categories = df['final_ultimate_category'].nunique()
        batch_size = self.get_l4_optimal_batch_size(num_categories)
        
        logger.info(f"L4 Training config - Total samples: {len(df)}")
        logger.info(f"Categories: {num_categories}, Batch size: {batch_size}")
        logger.info(f"Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
        
        all_results = []
        best_overall_loss = float('inf')
        start_level = 1
        start_epoch = 1
        
        # Resume from checkpoint if requested
        if resume_from:
            checkpoint = self.load_checkpoint(resume_from)
            if checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                start_level = checkpoint['level']
                start_epoch = checkpoint['epoch'] + 1
                best_overall_loss = checkpoint['best_loss']
                logger.info(f"Resumed from Level {start_level}, Epoch {checkpoint['epoch']}")
        
        # Progressive training through 3 levels
        for difficulty_level in range(start_level, 4):
            logger.info(f"\n{'='*70}")
            logger.info(f"PROGRESSIVE LEVEL {difficulty_level}/3 - L4 GPU SERVER TRAINING")
            logger.info(f"{'='*70}")
            
            train_dataset, val_dataset, train_loader, val_loader = self.create_balanced_dataloaders(
                train_df, val_df, batch_size, difficulty_level
            )
            
            # FIXED: Đồng bộ learning rate strategy từ local (conservative)
            base_lr = 2e-5  # Đồng bộ với local
            lr = base_lr * (0.8 ** (difficulty_level - 1))  # Gentler reduction như local

            optimizer = torch.optim.AdamW(
                [p for p in self.model.parameters() if p.requires_grad],
                lr=lr,
                weight_decay=0.01,
                eps=1e-8,
                betas=(0.9, 0.999)  # Đồng bộ với local
            )

            # FIXED: Sử dụng ConstantLR như local để tránh LR decay về 0
            total_steps = len(train_loader) * num_epochs_per_level
            scheduler = torch.optim.lr_scheduler.ConstantLR(
                optimizer,
                factor=1.0,  # Keep LR constant như local
                total_iters=total_steps
            )
            
            # Load optimizer state if resuming
            if resume_from and difficulty_level == start_level:
                checkpoint = self.load_checkpoint(resume_from)
                if checkpoint:
                    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            logger.info(f"Level {difficulty_level} - Categories: {len(train_dataset.filtered_categories)}")
            logger.info(f"Epochs: {num_epochs_per_level}, Batch size: {batch_size}, LR: {lr:.2e}")
            logger.info(f"Total steps: {total_steps}, Warmup steps: {total_steps // 20}")
            
            level_results = self._train_level_with_checkpoints(
                difficulty_level, num_epochs_per_level, train_loader, val_loader,
                optimizer, scheduler, train_dataset, val_dataset,
                start_epoch if difficulty_level == start_level else 1
            )
            
            all_results.append(level_results)
            start_epoch = 1  # Reset for next level
            
            if level_results['best_val_loss'] < best_overall_loss:
                best_overall_loss = level_results['best_val_loss']
                self.save_model_versions(self.model, self.tokenizer, f"best_level_{difficulty_level}")
                logger.info(f"New best model saved (loss: {best_overall_loss:.4f})")
        
        # Save final model
        final_model_path = self.save_model_versions(self.model, self.tokenizer, "final")
        
        total_time = time.time() - start_time
        
        # Final results
        final_results = {
            'server_type': 'L4_GPU',
            'dataset_size': len(df),
            'total_categories': num_categories,
            'difficulty_levels': 3,
            'epochs_per_level': num_epochs_per_level,
            'batch_size': batch_size,
            'train_samples': len(train_df),
            'val_samples': len(val_df),
            'test_samples': len(test_df),
            'total_time_hours': total_time / 3600,
            'best_overall_loss': float(best_overall_loss),
            'level_results': all_results,
            'final_model_path': str(final_model_path),
            'checkpoint_dir': str(self.checkpoint_dir)
        }
        
        with open(f"{self.output_dir}/l4_server_training_results.json", 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n{'='*70}")
        logger.info("L4 SERVER PROGRESSIVE TRAINING COMPLETED")
        logger.info(f"Total dataset: {len(df)} samples")
        logger.info(f"Total categories: {num_categories}")
        logger.info(f"Total time: {total_time/3600:.2f} hours") 
        logger.info(f"Best overall loss: {best_overall_loss:.4f}")
        logger.info(f"Final model saved to: {final_model_path}")
        logger.info(f"Checkpoints saved to: {self.checkpoint_dir}")
        logger.info(f"{'='*70}")
        
        return final_results
    
    def _train_level_with_checkpoints(self, level, num_epochs, train_loader, val_loader,
                                    optimizer, scheduler, train_dataset, val_dataset, start_epoch=1):
        """Train single difficulty level với checkpoint support"""
        level_train_losses = []
        level_val_losses = []
        best_level_loss = float('inf')
        
        for epoch in range(start_epoch, num_epochs + 1):
            epoch_start_time = time.time()
            logger.info(f"\nLevel {level}, Epoch {epoch}/{num_epochs}")
            
            # Training
            self.model.train()
            epoch_loss = 0.0
            num_batches = 0
            category_seen = set()
            
            # FIXED: Đồng bộ training loop từ local với strict batch limit
            for batch_idx, batch in enumerate(train_loader):
                # FIXED: Strict batch limit enforcement như local
                if batch_idx >= len(train_loader):
                    logger.info(f"Completed epoch {epoch} with {batch_idx} batches")
                    break

                # Track category diversity
                batch_categories = set(batch['categories'])
                category_seen.update(batch_categories)

                # Compute loss
                loss = enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)

                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()

                epoch_loss += loss.item()
                num_batches += 1

                # Memory cleanup
                del loss, batch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # Progress logging (ít hơn local vì server có nhiều batch)
                if (batch_idx + 1) % 50 == 0:  # Log mỗi 50 batches thay vì 10
                    avg_loss = epoch_loss / num_batches
                    lr = scheduler.get_last_lr()[0]
                    diversity = len(batch_categories)
                    elapsed = time.time() - epoch_start_time
                    logger.info(f"L{level} E{epoch} Batch {batch_idx+1}/{len(train_loader)}, "
                               f"Loss: {avg_loss:.4f}, LR: {lr:.2e}, Diversity: {diversity}, "
                               f"Time: {elapsed:.1f}s")
                
                avg_train_loss = epoch_loss / max(num_batches, 1)
                level_train_losses.append(avg_train_loss)
                
                logger.info(f"Epoch {epoch} category coverage: {len(category_seen)}/{len(train_dataset.filtered_categories)}")
                
            except Exception as e:
                logger.error(f"Training error in epoch {epoch}: {e}")
                # Save emergency checkpoint
                self.save_checkpoint(epoch, level, self.model, optimizer, scheduler, 
                                   level_train_losses, level_val_losses, best_level_loss)
                raise
            
            # Validation
            logger.info("Running validation...")
            self.model.eval()
            val_loss = 0.0
            val_batches = 0
            
            try:
                with torch.no_grad():
                    for val_batch_idx, batch in enumerate(val_loader):
                        # FIXED: Strict validation batch limit như local
                        if val_batch_idx >= len(val_loader):
                            break

                        loss = enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)
                        val_loss += loss.item()
                        val_batches += 1

                        del loss, batch
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()

                        if (val_batch_idx + 1) % 20 == 0:  # Log ít hơn cho server
                            logger.debug(f"Val batch {val_batch_idx+1}/{len(val_loader)}")

                avg_val_loss = val_loss / max(val_batches, 1)
                level_val_losses.append(avg_val_loss)

                # Save best model for this level
                if avg_val_loss < best_level_loss:
                    best_level_loss = avg_val_loss
                    level_best_path = self.save_model_versions(self.model, self.tokenizer, f"level_{level}_best")
                    logger.info(f"Level {level} best model saved: {level_best_path}")

            except Exception as e:
                logger.error(f"Validation error in epoch {epoch}: {e}")
                avg_val_loss = float('inf')
                level_val_losses.append(avg_val_loss)
            
            epoch_time = time.time() - epoch_start_time
            
            logger.info(f"Level {level} Epoch {epoch} - Train: {avg_train_loss:.4f}, "
                       f"Val: {avg_val_loss:.4f}, Best: {best_level_loss:.4f}, "
                       f"Time: {epoch_time:.1f}s")
            
            # Save checkpoint every epoch
            checkpoint_path = self.save_checkpoint(
                epoch, level, self.model, optimizer, scheduler, 
                level_train_losses, level_val_losses, best_level_loss
            )
            
            # Early stopping check (optional)
            if len(level_val_losses) >= 3:
                recent_losses = level_val_losses[-3:]
                if all(loss >= recent_losses[0] for loss in recent_losses[1:]):
                    logger.info(f"Early stopping triggered at epoch {epoch}")
                    break
        
        # Save final level model
        final_level_path = self.save_model_versions(self.model, self.tokenizer, f"level_{level}_final")
        
        return {
            'level': level,
            'num_epochs': len(level_train_losses),
            'categories_count': len(train_dataset.filtered_categories),
            'train_losses': level_train_losses,
            'val_losses': level_val_losses,
            'best_val_loss': float(best_level_loss),
            'final_train_loss': float(level_train_losses[-1]) if level_train_losses else float('inf'),
            'final_val_loss': float(level_val_losses[-1]) if level_val_losses else float('inf'),
            'final_model_path': str(final_level_path)
        }

def main():
    """Main training function for L4 Server"""
    logger.info("Starting L4 Server Progressive Vintern Training")
    logger.info("700k samples, full Vietnamese food dataset")
    
    # Parse command line arguments for resume functionality
    import argparse
    parser = argparse.ArgumentParser(description='L4 Server Vintern Training')
    parser.add_argument('--resume', type=str, help='Path to checkpoint to resume from')
    parser.add_argument('--epochs', type=int, default=3, help='Epochs per difficulty level')
    parser.add_argument('--model_path', type=str, default='Vintern-1B-v3_5', help='Base model path')
    parser.add_argument('--output_dir', type=str, default='vintern_l4_server_final', help='Output directory')
    
    args = parser.parse_args()
    
    trainer = ServerL4ProgressiveTrainer(
        model_path=args.model_path,
        output_dir=args.output_dir
    )
    
    try:
        results = trainer.progressive_train_with_checkpoints(
            num_epochs_per_level=args.epochs,
            resume_from=args.resume
        )
        
        print("\n" + "="*70)
        print("L4 SERVER PROGRESSIVE TRAINING COMPLETED!")
        print("="*70)
        print(f"Dataset size: {results['dataset_size']} samples")
        print(f"Total categories: {results['total_categories']}")
        print(f"Best validation loss: {results['best_overall_loss']:.4f}")
        print(f"Total training time: {results['total_time_hours']:.2f} hours")
        print(f"Batch size: {results['batch_size']} (L4 optimized)")
        print(f"Data split: Train {results['train_samples']}, Val {results['val_samples']}, Test {results['test_samples']}")
        print(f"Final model: {results['final_model_path']}")
        print(f"Checkpoints: {results['checkpoint_dir']}")
        print("="*70)
        
        # Test model loading
        logger.info("Testing final model loading...")
        from transformers import AutoTokenizer, AutoModel
        test_tokenizer = AutoTokenizer.from_pretrained(results['final_model_path'], trust_remote_code=True)
        test_model = AutoModel.from_pretrained(results['final_model_path'], trust_remote_code=True)
        logger.info("✓ Final model loads successfully")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        print("\nTraining stopped. Checkpoints are saved in the checkpoints directory.")
        print(f"Resume with: python {__file__} --resume <checkpoint_path>")
    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"\nTraining failed: {e}")
        print("Check logs and checkpoints for recovery options.")
        raise

def list_checkpoints(checkpoint_dir="vintern_l4_server_final/checkpoints"):
    """Utility function to list available checkpoints"""
    checkpoint_path = Path(checkpoint_dir)
    if not checkpoint_path.exists():
        print(f"Checkpoint directory {checkpoint_dir} not found")
        return
    
    checkpoints = sorted(checkpoint_path.glob("checkpoint_*.pt"))
    print(f"\nAvailable checkpoints in {checkpoint_dir}:")
    print("-" * 60)
    
    for cp in checkpoints:
        try:
            info = torch.load(cp, map_location='cpu')
            timestamp = info.get('timestamp', 'Unknown')
            level = info.get('level', 'Unknown')
            epoch = info.get('epoch', 'Unknown')
            best_loss = info.get('best_loss', 'Unknown')
            
            print(f"{cp.name}")
            print(f"  Level: {level}, Epoch: {epoch}")
            print(f"  Best Loss: {best_loss}")
            print(f"  Timestamp: {timestamp}")
            print()
        except Exception as e:
            print(f"{cp.name} - Error loading: {e}")
    
    if checkpoints:
        latest = checkpoints[-1]
        print(f"Latest checkpoint: {latest.name}")
        print(f"Resume with: python {__file__} --resume {latest}")
    else:
        print("No checkpoints found")

if __name__ == "__main__":
    import sys
    
    # Special command to list checkpoints
    if len(sys.argv) == 2 and sys.argv[1] == "--list-checkpoints":
        list_checkpoints()
    elif len(sys.argv) == 3 and sys.argv[1] == "--list-checkpoints":
        list_checkpoints(sys.argv[2])
    else:
        main()