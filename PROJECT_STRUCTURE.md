# VLM Project Structure - Organized for Git

## 📋 Overview

Project đã được tổ chức lại để dễ quản lý và push lên Git. Cấu trúc mới giữ nguyên code gốc và tạo thêm phiên bản tổ chức tốt hơn.

## 📁 Cấu trúc mới (Organized)

```
vlm_model/
├── src/                          # 🆕 Code đã tổ chức
│   ├── __init__.py
│   ├── preprocessing.py          # Từ Preprocessing_Data/
│   ├── finetune_multi_lora.py   # Từ Multi_LoRA_Finetune/
│   ├── finetune_progressive.py  # Từ Progressive_Method_Finetune/
│   ├── test_methods.py          # Từ Method_For_Test_With_Model_VinternV3.5/
│   └── ui.py                    # Từ UI_For_VLM/
├── models/                       # 🆕 Trained models
│   └── README.md
├── data/                         # 🆕 Data files (ignored)
│   └── example_data.csv
├── tests/                        # 🆕 Unit tests
│   ├── __init__.py
│   └── test_preprocessing.py
├── docs/                         # 🆕 Documentation
│   └── architecture.md
├── notebooks/                    # 🆕 Jupyter experiments
│   └── finetune_experiments.ipynb
├── requirements.txt              # 🆕 Dependencies
├── main.py                       # 🆕 Entry point
├── .gitignore                    # 🆕 Git ignore rules
└── README_NEW.md                 # 🆕 Updated README
```

## 📁 Cấu trúc gốc (Preserved)

```
vlm_model/
├── Multi_LoRA_Finetune/         # ✅ Giữ nguyên
├── Progressive_Method_Finetune/  # ✅ Giữ nguyên
├── Method_For_Test_With_Model_VinternV3.5/ # ✅ Giữ nguyên
├── UI_For_VLM/                  # ✅ Giữ nguyên
├── Preprocessing_Data/          # ✅ Giữ nguyên
├── Model_Vintern/               # ✅ Giữ nguyên
├── Original_Data/               # ✅ Giữ nguyên
└── README.md                    # ✅ README gốc
```

## 🎯 Lợi ích của cấu trúc mới

### ✅ Dễ sử dụng
- **main.py**: Entry point duy nhất cho tất cả chức năng
- **src/**: Code được tổ chức theo module rõ ràng
- **requirements.txt**: Dependencies được liệt kê đầy đủ

### ✅ Git-friendly
- **.gitignore**: Loại bỏ file lớn và không cần thiết
- **Cấu trúc rõ ràng**: Dễ review và collaborate
- **Documentation**: README và docs/ đầy đủ

### ✅ Development-ready
- **tests/**: Unit tests sẵn sàng
- **notebooks/**: Jupyter experiments
- **docs/**: Documentation chi tiết

## 🚀 Cách sử dụng

### 1. Sử dụng cấu trúc mới (Recommended)
```bash
# Preprocessing
python main.py preprocess --input data/raw.csv --output data/processed.csv

# Training
python main.py train multi-lora --data data/processed.csv

# Testing
python main.py test --model models/my_model --data data/test.csv

# UI
python main.py ui --model models/my_model
```

### 2. Sử dụng code gốc (Backward compatibility)
```bash
# Vẫn có thể chạy code gốc như trước
cd Multi_LoRA_Finetune/
python run_multi_lora_training.py

cd Progressive_Method_Finetune/
python run_improved_training.py
```

## 📦 Git Management

### Files được push lên Git:
- ✅ Source code (src/)
- ✅ Documentation (docs/, README)
- ✅ Configuration (requirements.txt, .gitignore)
- ✅ Tests (tests/)
- ✅ Small examples (data/example_data.csv)

### Files được ignore:
- ❌ Large datasets (*.csv > 50MB)
- ❌ Model files (*.pth, *.safetensors)
- ❌ Logs (*.log)
- ❌ Cache (__pycache__/)
- ❌ Environment files (.env)

## 🔄 Migration Guide

### Nếu đang dùng code cũ:
1. **Không cần thay đổi gì** - code cũ vẫn hoạt động
2. **Muốn dùng mới**: Import từ src/
   ```python
   from src.preprocessing import PreprocessingPipeline
   from src.finetune_multi_lora import create_multi_lora_model
   ```

### Nếu muốn contribute:
1. **Thêm feature mới**: Đặt trong src/
2. **Thêm test**: Đặt trong tests/
3. **Update docs**: Cập nhật docs/

## 🎉 Kết luận

Project giờ đã:
- ✅ **Organized**: Cấu trúc rõ ràng, dễ hiểu
- ✅ **Git-ready**: Sẵn sàng push lên Git
- ✅ **Backward compatible**: Code cũ vẫn hoạt động
- ✅ **Future-proof**: Dễ mở rộng và maintain

**Ready to push to Git! 🚀**
