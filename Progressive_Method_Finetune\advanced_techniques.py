#!/usr/bin/env python3
"""
Advanced Training Techniques cho Vintern VLM
- Knowledge Distillation từ larger models
- Self-supervised pretraining
- Data augmentation nâng cao
- Ensemble methods
- Active learning cho hard samples
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
from transformers import AutoModel, AutoTokenizer
import numpy as np
import random
from typing import Dict, List, Tuple, Optional

class KnowledgeDistillation:
    """Knowledge distillation từ teacher model lớn hơn"""
    
    def __init__(self, teacher_model_path, student_model, temperature=3.0, alpha=0.7):
        self.teacher_model = AutoModel.from_pretrained(
            teacher_model_path, 
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
        self.teacher_model.eval()
        
        self.student_model = student_model
        self.temperature = temperature
        self.alpha = alpha  # Weight for distillation loss
        
    def distillation_loss(self, student_logits, teacher_logits, targets, hard_loss_fn):
        """Compute knowledge distillation loss"""
        # Soft targets từ teacher
        soft_targets = F.softmax(teacher_logits / self.temperature, dim=1)
        soft_student = F.log_softmax(student_logits / self.temperature, dim=1)
        
        # Distillation loss
        distill_loss = F.kl_div(soft_student, soft_targets, reduction='batchmean')
        distill_loss *= (self.temperature ** 2)
        
        # Hard loss từ ground truth
        hard_loss = hard_loss_fn(student_logits, targets)
        
        # Combined loss
        total_loss = self.alpha * distill_loss + (1 - self.alpha) * hard_loss
        
        return total_loss, distill_loss.item(), hard_loss.item()
    
    def get_teacher_predictions(self, images, instructions):
        """Get teacher model predictions"""
        with torch.no_grad():
            # Teacher forward pass
            teacher_outputs = []
            for i in range(images.size(0)):
                try:
                    output = self.teacher_model.chat(
                        tokenizer=None,  # Will be provided
                        images=images[i:i+1],
                        query=instructions[i],
                        generation_config=dict(
                            max_new_tokens=100,
                            do_sample=False,
                            temperature=0.1
                        )
                    )
                    teacher_outputs.append(output)
                except:
                    teacher_outputs.append("")
            
            return teacher_outputs

class AdvancedDataAugmentation:
    """Advanced data augmentation cho VLM"""
    
    def __init__(self, augment_prob=0.5):
        self.augment_prob = augment_prob
        
        # Image augmentations
        self.image_augs = transforms.Compose([
            transforms.RandomApply([
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.1)
            ], p=0.5),
            transforms.RandomApply([
                transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0))
            ], p=0.3),
            transforms.RandomApply([
                transforms.RandomAffine(degrees=10, translate=(0.1, 0.1), scale=(0.9, 1.1))
            ], p=0.4),
            transforms.RandomApply([
                transforms.RandomPerspective(distortion_scale=0.2)
            ], p=0.3)
        ])
        
        # Text augmentations
        self.text_templates = [
            "What Vietnamese food is shown in this image?",
            "Identify this Vietnamese dish.",
            "What is the name of this Vietnamese food?",
            "Can you recognize this Vietnamese cuisine?",
            "What Vietnamese meal is displayed here?",
            "Name this traditional Vietnamese dish.",
            "What type of Vietnamese food is this?",
            "Describe this Vietnamese culinary item."
        ]
    
    def augment_image(self, image):
        """Apply image augmentation"""
        if random.random() < self.augment_prob:
            return self.image_augs(image)
        return image
    
    def augment_instruction(self, instruction, difficulty_level=1):
        """Augment instruction text"""
        if random.random() < self.augment_prob:
            if difficulty_level == 1:
                return random.choice(self.text_templates[:4])
            elif difficulty_level == 2:
                return random.choice(self.text_templates[2:6])
            else:
                return random.choice(self.text_templates[4:])
        return instruction
    
    def mixup_images(self, images, labels, alpha=0.2):
        """Mixup augmentation cho images"""
        if random.random() < self.augment_prob:
            batch_size = images.size(0)
            indices = torch.randperm(batch_size)
            
            lam = np.random.beta(alpha, alpha)
            mixed_images = lam * images + (1 - lam) * images[indices]
            
            return mixed_images, labels, labels[indices], lam
        
        return images, labels, None, 1.0

class SelfSupervisedPretraining:
    """Self-supervised pretraining tasks"""
    
    def __init__(self, model):
        self.model = model
        
        # Pretext task heads
        self.rotation_head = nn.Linear(model.config.hidden_size, 4)  # 0, 90, 180, 270 degrees
        self.jigsaw_head = nn.Linear(model.config.hidden_size, 24)   # Jigsaw puzzle permutations
        
    def rotation_task(self, images):
        """Image rotation prediction task"""
        batch_size = images.size(0)
        
        # Create rotated versions
        rotations = [0, 90, 180, 270]
        rotated_images = []
        rotation_labels = []
        
        for img in images:
            rot_idx = random.randint(0, 3)
            rotation = rotations[rot_idx]
            
            # Rotate image
            if rotation == 90:
                rotated_img = torch.rot90(img, k=1, dims=[1, 2])
            elif rotation == 180:
                rotated_img = torch.rot90(img, k=2, dims=[1, 2])
            elif rotation == 270:
                rotated_img = torch.rot90(img, k=3, dims=[1, 2])
            else:
                rotated_img = img
            
            rotated_images.append(rotated_img)
            rotation_labels.append(rot_idx)
        
        rotated_images = torch.stack(rotated_images)
        rotation_labels = torch.tensor(rotation_labels, device=images.device)
        
        # Forward pass
        features = self.model.vision_model(rotated_images)
        pooled_features = features.last_hidden_state.mean(dim=1)
        
        # Predict rotation
        rotation_logits = self.rotation_head(pooled_features)
        rotation_loss = F.cross_entropy(rotation_logits, rotation_labels)
        
        return rotation_loss
    
    def contrastive_task(self, images):
        """Contrastive learning task"""
        batch_size = images.size(0)
        
        # Create augmented pairs
        aug_transform = transforms.Compose([
            transforms.RandomResizedCrop(224, scale=(0.8, 1.0)),
            transforms.RandomHorizontalFlip(),
            transforms.ColorJitter(0.4, 0.4, 0.4, 0.1),
            transforms.RandomGrayscale(p=0.2)
        ])
        
        # Generate positive pairs
        images_aug1 = torch.stack([aug_transform(img) for img in images])
        images_aug2 = torch.stack([aug_transform(img) for img in images])
        
        # Get embeddings
        features1 = self.model.vision_model(images_aug1)
        features2 = self.model.vision_model(images_aug2)
        
        emb1 = F.normalize(features1.last_hidden_state.mean(dim=1), p=2, dim=1)
        emb2 = F.normalize(features2.last_hidden_state.mean(dim=1), p=2, dim=1)
        
        # Contrastive loss (SimCLR style)
        temperature = 0.07
        
        # Concatenate embeddings
        embeddings = torch.cat([emb1, emb2], dim=0)  # [2*B, D]
        
        # Compute similarity matrix
        sim_matrix = torch.matmul(embeddings, embeddings.T) / temperature
        
        # Create labels (positive pairs)
        labels = torch.cat([torch.arange(batch_size), torch.arange(batch_size)], dim=0)
        labels = labels.to(images.device)
        
        # Mask out self-similarity
        mask = torch.eye(2 * batch_size, device=images.device).bool()
        sim_matrix.masked_fill_(mask, -float('inf'))
        
        # Contrastive loss
        contrastive_loss = F.cross_entropy(sim_matrix, labels)
        
        return contrastive_loss

class ActiveLearning:
    """Active learning cho hard samples"""
    
    def __init__(self, model, uncertainty_threshold=0.8):
        self.model = model
        self.uncertainty_threshold = uncertainty_threshold
        self.hard_samples = []
    
    def compute_uncertainty(self, logits):
        """Compute prediction uncertainty"""
        probs = F.softmax(logits, dim=1)
        
        # Entropy-based uncertainty
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=1)
        max_entropy = torch.log(torch.tensor(probs.size(1), dtype=torch.float))
        normalized_entropy = entropy / max_entropy
        
        return normalized_entropy
    
    def identify_hard_samples(self, batch, logits):
        """Identify hard samples for additional training"""
        uncertainties = self.compute_uncertainty(logits)
        
        hard_indices = uncertainties > self.uncertainty_threshold
        
        if hard_indices.any():
            # Store hard samples
            for i, is_hard in enumerate(hard_indices):
                if is_hard:
                    hard_sample = {
                        'image': batch['images'][i],
                        'instruction': batch['instructions'][i],
                        'response': batch['responses'][i],
                        'category': batch['categories'][i],
                        'uncertainty': uncertainties[i].item()
                    }
                    self.hard_samples.append(hard_sample)
        
        return hard_indices.sum().item()
    
    def get_hard_samples_batch(self, batch_size=8):
        """Get batch of hard samples for additional training"""
        if len(self.hard_samples) < batch_size:
            return None
        
        # Sample hard examples
        selected = random.sample(self.hard_samples, batch_size)
        
        batch = {
            'images': torch.stack([s['image'] for s in selected]),
            'instructions': [s['instruction'] for s in selected],
            'responses': [s['response'] for s in selected],
            'categories': [s['category'] for s in selected]
        }
        
        return batch

class EnsembleTraining:
    """Ensemble training methods"""
    
    def __init__(self, models: List[nn.Module]):
        self.models = models
        self.num_models = len(models)
    
    def ensemble_predict(self, images, instructions):
        """Ensemble prediction từ multiple models"""
        all_predictions = []
        
        for model in self.models:
            model.eval()
            with torch.no_grad():
                # Get predictions from each model
                predictions = []
                for i in range(images.size(0)):
                    try:
                        pred = model.chat(
                            tokenizer=None,
                            images=images[i:i+1],
                            query=instructions[i]
                        )
                        predictions.append(pred)
                    except:
                        predictions.append("")
                
                all_predictions.append(predictions)
        
        # Ensemble by voting or averaging
        ensemble_predictions = []
        for i in range(len(all_predictions[0])):
            # Simple majority voting for now
            sample_preds = [preds[i] for preds in all_predictions]
            # Could implement more sophisticated ensemble methods
            ensemble_pred = max(set(sample_preds), key=sample_preds.count)
            ensemble_predictions.append(ensemble_pred)
        
        return ensemble_predictions
    
    def diverse_training(self, train_loaders: List):
        """Train models với diverse data/augmentation"""
        for i, (model, train_loader) in enumerate(zip(self.models, train_loaders)):
            logger.info(f"Training ensemble model {i+1}/{self.num_models}")
            
            # Each model gets different augmentation/data
            # Implementation would depend on specific training loop
            pass

def create_advanced_trainer(base_trainer, techniques: List[str]):
    """Factory function để tạo advanced trainer"""
    
    if 'knowledge_distillation' in techniques:
        # Add knowledge distillation
        kd = KnowledgeDistillation("larger_teacher_model", base_trainer.model)
        base_trainer.knowledge_distillation = kd
    
    if 'advanced_augmentation' in techniques:
        # Add advanced augmentation
        aug = AdvancedDataAugmentation()
        base_trainer.data_augmentation = aug
    
    if 'self_supervised' in techniques:
        # Add self-supervised pretraining
        ssl = SelfSupervisedPretraining(base_trainer.model)
        base_trainer.self_supervised = ssl
    
    if 'active_learning' in techniques:
        # Add active learning
        al = ActiveLearning(base_trainer.model)
        base_trainer.active_learning = al
    
    return base_trainer
