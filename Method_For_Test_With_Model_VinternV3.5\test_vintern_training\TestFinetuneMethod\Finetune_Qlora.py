#!/usr/bin/env python3
"""
APPROACH 7: QLoRA (Quantized LoRA)
Kết hợp 4-bit quantization + LoRA để tiết kiệm memory tối đa
"""

import os
import torch
import pandas as pd
import logging
import random
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QLoRADataset(Dataset):
    """
    Dataset cho QLoRA training
    Focus vào high-quality examples để maximize efficiency
    """
    def __init__(self, df, tokenizer, max_length=140):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.qlora_data = self.create_qlora_examples()
        
        logger.info(f"Tạo QLoRADataset với {len(self.qlora_data)} high-quality examples")
    
    def create_qlora_examples(self):
        """Tạo high-quality examples cho QLoRA"""
        
        # QLoRA works best với high-quality, diverse examples
        quality_templates = [
            ("Nhận diện món ăn Việt Nam: {food_name}", "high_quality"),
            ("Tên món ăn: {food_name}", "simple"),
            ("Đây là món: {food_name}", "descriptive"),
            ("Món ăn truyền thống: {food_name}", "cultural"),
            ("Ẩm thực: {food_name}", "cuisine")
        ]
        
        qlora_data = []
        
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            
            # Filter for quality (length, no special chars)
            if len(food_name) < 3 or len(food_name) > 30:
                continue
            if any(char.isdigit() for char in food_name):
                continue
            
            # Tạo 1-2 high-quality examples per food
            selected_templates = random.sample(quality_templates, min(2, len(quality_templates)))
            
            for template, quality_type in selected_templates:
                text = template.format(food_name=food_name)
                
                qlora_data.append({
                    'food_name': food_name,
                    'quality_type': quality_type,
                    'text': text
                })
        
        # Sort by quality for curriculum learning effect
        qlora_data.sort(key=lambda x: len(x['food_name']))
        
        return qlora_data
    
    def __len__(self):
        return len(self.qlora_data)
    
    def __getitem__(self, idx):
        item = self.qlora_data[idx]
        text = item['text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'quality_type': item['quality_type']
        }

def setup_qlora_model(model_path):
    """
    Setup QLoRA với 4-bit quantization
    
    QLoRA innovations:
    1. 4-bit NormalFloat quantization
    2. Double quantization
    3. Paged optimizers
    """
    logger.info("=== THIẾT LẬP QLORA ===")
    
    # QLoRA quantization config
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,                      # 4-bit quantization
        bnb_4bit_use_double_quant=True,         # Double quantization
        bnb_4bit_quant_type="nf4",              # NormalFloat4
        bnb_4bit_compute_dtype=torch.float16,   # Compute in fp16
    )
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model với quantization
    logger.info("Loading model với 4-bit quantization...")
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16
    )
    
    # Prepare model for k-bit training
    model = prepare_model_for_kbit_training(model)
    
    # QLoRA config - optimized for quantized model
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        qlora_config = LoraConfig(
            r=64,                    # Higher rank for QLoRA
            lora_alpha=128,          # Higher alpha
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.01,       # Very low dropout
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        model.language_model = get_peft_model(llm, qlora_config)
        logger.info("✅ QLoRA applied")
        model.language_model.print_trainable_parameters()
    
    return model, tokenizer

def train_qlora_model(model, train_dataloader, device, num_epochs=1):
    """
    Training QLoRA model với memory-efficient techniques
    """
    logger.info("=== BẮT ĐẦU QLORA TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    # QLoRA optimizer - AdamW với paged attention
    optimizer = torch.optim.AdamW(
        trainable_params, 
        lr=1e-4,                    # Conservative LR for quantized model
        weight_decay=0.01,
        betas=(0.9, 0.95)          # Optimized betas for QLoRA
    )
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    max_steps = 100  # More steps for QLoRA convergence
    
    quality_losses = {'high_quality': [], 'simple': [], 'descriptive': [], 'cultural': [], 'cuisine': []}
    
    for epoch in range(num_epochs):
        logger.info(f"--- QLORA EPOCH {epoch + 1}/{num_epochs} ---")
        
        for batch in tqdm(train_dataloader, desc=f"QLoRA Training"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            quality_types = batch['quality_type']
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                
                # Track loss per quality type
                for i, quality_type in enumerate(quality_types):
                    if quality_type in quality_losses:
                        quality_losses[quality_type].append(loss.item())
                
                optimizer.zero_grad()
                loss.backward()
                
                # Gradient clipping for stability với quantized weights
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=0.3)
                
                optimizer.step()
                
                step += 1
                
                if step % 20 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps} - Loss: {loss.item():.4f} - Avg Loss: {avg_loss:.4f}")
                    
                    # Memory usage
                    if torch.cuda.is_available():
                        memory_used = torch.cuda.memory_allocated() / 1024**3
                        logger.info(f"  GPU Memory: {memory_used:.2f} GB")
                
                training_history.append({
                    'step': step,
                    'loss': loss.item(),
                    'avg_loss': total_loss / step
                })
                
            except Exception as e:
                logger.error(f"QLoRA training error: {str(e)}")
                step += 1
                continue
        
        if step >= max_steps:
            break
    
    logger.info("✅ QLoRA training completed!")
    return model, training_history

def test_qlora_model(model, tokenizer, device):
    """Test QLoRA model"""
    logger.info("=== TESTING QLORA MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test prompts for QLoRA
    test_prompts = [
        "Nhận diện món ăn Việt Nam:",
        "Tên món ăn:",
        "Đây là món:",
        "Món ăn truyền thống:",
        "Ẩm thực:"
    ]
    
    test_results = []
    
    for prompt in test_prompts:
        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 15,
                    temperature=0.4,  # Lower temperature for quantized model
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            
            test_results.append({
                'prompt': prompt,
                'generated': generated
            })
            
            logger.info(f"'{prompt}' → '{generated}'")
            
        except Exception as e:
            logger.warning(f"QLoRA generation failed: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 7: QLORA")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach7_qlora_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Setup QLoRA model
    model, tokenizer = setup_qlora_model(model_path)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(180).dropna(subset=['food_name'])  # Smaller for QLoRA efficiency
    
    # Create QLoRA dataset
    train_dataset = QLoRADataset(train_df, tokenizer)
    train_dataloader = DataLoader(train_dataset, batch_size=1, shuffle=True)  # Small batch for memory
    
    logger.info(f"QLoRA examples: {len(train_dataset)}")
    
    # Training
    model, history = train_qlora_model(model, train_dataloader, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_qlora_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'QLoRA (Quantized LoRA)',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'qlora_examples': len(train_dataset),
        'quantization': '4-bit NF4',
        'memory_efficient': True
    }
    
    with open('output_qlora.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 7: QLORA RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"QLoRA examples: {results['qlora_examples']}\n")
        f.write(f"Quantization: {results['quantization']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']}: Loss={entry['loss']:.4f}, Avg Loss={entry['avg_loss']:.4f}\n")
        
        f.write("\nQLoRA Test Results:\n")
        for result in test_results:
            f.write(f"'{result['prompt']}' → '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 7 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_qlora.txt")

if __name__ == "__main__":
    main()
