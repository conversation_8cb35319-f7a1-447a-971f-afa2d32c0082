#!/usr/bin/env python3
"""
FIXED CATEGORY-BALANCED Progressive Vintern v3.5 Training
- Fixed infinite loop in BalancedBatchSampler  
- Fixed LR scheduler going to 0
- Added proper epoch termination
- Conservative memory management
"""

import torch
import json
import logging
import time
from PIL import Image
import requests
from io import BytesIO
import torchvision.transforms as transforms
from transformers import AutoTokenizer, AutoModel, get_linear_schedule_with_warmup
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import gc
import random
from collections import defaultdict, Counter
import pandas as pd
import math

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vintern_balanced_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CategoryBalancedDataset(Dataset):
    """Category-balanced dataset ensuring diverse sampling"""
    
    def __init__(self, data_df, tokenizer, difficulty_level=1, is_training=True):
        self.tokenizer = tokenizer
        self.difficulty_level = difficulty_level
        self.is_training = is_training
        
        # Organize data by category
        self.category_data = defaultdict(list)
        self.categories = []
        
        for _, row in data_df.iterrows():
            category = row['final_ultimate_category']
            self.category_data[category].append({
                'food_name': row['food_name'],
                'category': category,
                'image_url': row['URL'],
                'normalized_name': row['normalized_name']
            })
        
        self.categories = list(self.category_data.keys())
        
        # Progressive filtering by difficulty
        self.filtered_categories = self._filter_categories_by_difficulty()
        
        # Calculate category complexity scores for progressive learning
        self.category_complexity = self._calculate_category_complexity()
        
        self.transform = transforms.Compose([
            transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        logger.info(f"Dataset Level {difficulty_level}: {len(self.filtered_categories)} categories, "
                   f"{sum(len(self.category_data[cat]) for cat in self.filtered_categories)} samples")
    
    def _calculate_category_complexity(self):
        """Calculate complexity score for each category"""
        complexity = {}
        
        for category in self.categories:
            score = 0
            
            # Name complexity (longer = harder)
            avg_name_length = np.mean([len(item['food_name'].split()) 
                                     for item in self.category_data[category]])
            score += min(avg_name_length, 5) * 10
            
            # Category name complexity
            cat_words = len(category.split())
            score += cat_words * 5
            
            # Normalize to 0-100
            complexity[category] = min(score, 100)
        
        return complexity
    
    def _filter_categories_by_difficulty(self):
        """Filter categories based on difficulty level"""
        if not hasattr(self, 'category_complexity'):
            # Initial calculation
            temp_complexity = {}
            for category in self.categories:
                score = len(category.split()) * 10 + np.random.randint(0, 30)
                temp_complexity[category] = score
            
            sorted_categories = sorted(temp_complexity.keys(), key=lambda x: temp_complexity[x])
        else:
            sorted_categories = sorted(self.categories, key=lambda x: self.category_complexity[x])
        
        if self.difficulty_level == 1:
            # Easy: first 1/3 of categories
            return sorted_categories[:len(sorted_categories)//3]
        elif self.difficulty_level == 2:
            # Medium: first 2/3 of categories  
            return sorted_categories[:2*len(sorted_categories)//3]
        else:
            # Hard: all categories
            return sorted_categories
    
    def update_difficulty(self, new_level):
        """Update difficulty level"""
        self.difficulty_level = new_level
        self.filtered_categories = self._filter_categories_by_difficulty()
        logger.info(f"Updated to level {new_level}: {len(self.filtered_categories)} categories")
    
    def __len__(self):
        """Total samples in filtered categories"""
        return sum(len(self.category_data[cat]) for cat in self.filtered_categories)
    
    def __getitem__(self, idx):
        """Get item by global index"""
        # Convert global index to (category, local_index)
        current_idx = 0
        for category in self.filtered_categories:
            cat_size = len(self.category_data[category])
            if current_idx + cat_size > idx:
                local_idx = idx - current_idx
                item = self.category_data[category][local_idx]
                break
            current_idx += cat_size
        else:
            # Fallback
            item = self.category_data[self.filtered_categories[0]][0]
        
        # Load and process image
        tensor = self._load_image(item['image_url'])
        
        # Progressive instructions
        instruction = self._get_progressive_instruction(item, self.difficulty_level)
        response = self._get_target_response(item, self.difficulty_level)
        
        return {
            'image_tensor': tensor,
            'instruction': instruction,
            'response': response,
            'food_name': item['food_name'],
            'category': item['category'],
            'difficulty': self.difficulty_level
        }
    
    def _load_image(self, image_url):
        """Load and process image with error handling"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(image_url, headers=headers, timeout=10, verify=False)
            response.raise_for_status()
            
            image = Image.open(BytesIO(response.content))
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # EXIF orientation handling
            if hasattr(image, '_getexif') and image._getexif() is not None:
                exif = image._getexif()
                if exif is not None and 274 in exif:
                    orientation = exif[274]
                    if orientation == 3:
                        image = image.rotate(180, expand=True)
                    elif orientation == 6:
                        image = image.rotate(270, expand=True)
                    elif orientation == 8:
                        image = image.rotate(90, expand=True)
            
            return self.transform(image).unsqueeze(0)
            
        except Exception as e:
            return torch.zeros(1, 3, 448, 448)
    
    def _get_progressive_instruction(self, item, level):
        """Generate progressive instructions"""
        if level == 1:
            return "What Vietnamese food is shown in this image?"
        elif level == 2:
            return f"Identify this Vietnamese dish and describe its category."
        else:
            return f"Analyze this Vietnamese food image. Provide the dish name, category, and key characteristics."
    
    def _get_target_response(self, item, level):
        """Generate target responses"""
        if level == 1:
            return f"This is {item['food_name']}."
        elif level == 2:
            return f"This is {item['food_name']}, which belongs to the {item['category']} category."
        else:
            return f"This is {item['food_name']}, a Vietnamese dish from the {item['category']} category. It represents traditional Vietnamese cuisine."

class FixedBalancedBatchSampler:
    """FIXED: Proper finite batch sampler with epoch control"""
    
    def __init__(self, dataset, batch_size, shuffle=True, max_batches_per_epoch=None):
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.categories = dataset.filtered_categories
        
        # Ensure batch_size doesn't exceed number of categories
        self.effective_batch_size = min(batch_size, len(self.categories))
        
        # Calculate reasonable max batches per epoch
        total_samples = sum(len(dataset.category_data[cat]) for cat in self.categories)
        min_samples_per_category = min(len(dataset.category_data[cat]) for cat in self.categories)
        
        # Each epoch should see each category at least once
        # Max batches = min samples per category (ensures fair representation)
        self.max_batches_per_epoch = max_batches_per_epoch or min(
            total_samples // self.effective_batch_size,
            min_samples_per_category * 2  # Conservative limit
        )
        
        logger.info(f"Fixed Balanced Sampler: {len(self.categories)} categories, "
                   f"effective batch size: {self.effective_batch_size}, "
                   f"max batches per epoch: {self.max_batches_per_epoch}")
    
    def __iter__(self):
        """Generate finite balanced batches per epoch"""
        # Create category indices
        category_indices = {}
        global_idx = 0
        
        for category in self.categories:
            cat_samples = len(self.dataset.category_data[category])
            category_indices[category] = list(range(global_idx, global_idx + cat_samples))
            global_idx += cat_samples
        
        # Shuffle indices within each category
        if self.shuffle:
            for category in category_indices:
                random.shuffle(category_indices[category])
        
        # Create category iterators
        category_iterators = {cat: iter(indices) for cat, indices in category_indices.items()}
        
        # Generate exactly max_batches_per_epoch batches
        batches_generated = 0
        
        while batches_generated < self.max_batches_per_epoch:
            batch = []
            selected_categories = (random.sample(self.categories, self.effective_batch_size) 
                                 if self.shuffle else self.categories[:self.effective_batch_size])
            
            for category in selected_categories:
                try:
                    idx = next(category_iterators[category])
                    batch.append(idx)
                except StopIteration:
                    # Restart iterator for this category
                    if self.shuffle:
                        random.shuffle(category_indices[category])
                    category_iterators[category] = iter(category_indices[category])
                    try:
                        idx = next(category_iterators[category])
                        batch.append(idx)
                    except StopIteration:
                        # If category is empty, use a random sample
                        if len(batch) > 0:  # Don't break if we have some samples
                            continue
                        break
            
            if len(batch) > 0:
                yield batch
                batches_generated += 1
            else:
                break
    
    def __len__(self):
        """Return fixed number of batches per epoch"""
        return self.max_batches_per_epoch

def balanced_collate_fn(batch):
    """Collate function for balanced batches"""
    images = torch.cat([item['image_tensor'] for item in batch], dim=0)
    instructions = [item['instruction'] for item in batch]
    responses = [item['response'] for item in batch]
    food_names = [item['food_name'] for item in batch]
    categories = [item['category'] for item in batch]
    difficulties = [item['difficulty'] for item in batch]
    
    # Log category diversity in batch
    unique_categories = len(set(categories))
    if len(batch) > 0 and random.random() < 0.1:  # Log 10% of batches
        logger.debug(f"Batch diversity: {unique_categories}/{len(batch)} unique categories")
    
    return {
        'images': images,
        'instructions': instructions,
        'responses': responses,
        'food_names': food_names,
        'categories': categories,
        'difficulties': difficulties
    }

def enhanced_compute_loss(model, tokenizer, batch, device):
    """Enhanced loss with category diversity awareness"""
    images = batch['images'].to(device, dtype=torch.float16 if torch.cuda.is_available() else torch.float32)
    instructions = batch['instructions']
    food_names = batch['food_names']
    categories = batch['categories']
    
    batch_size = images.size(0)
    total_loss = 0.0
    valid_samples = 0
    
    # Category diversity bonus
    unique_categories = len(set(categories))
    diversity_bonus = min(unique_categories / batch_size, 1.0) * 0.1
    
    for i in range(batch_size):
        try:
            single_image = images[i:i+1]
            
            generated = model.chat(
                tokenizer,
                single_image,
                instructions[i],
                generation_config=dict(
                    max_new_tokens=80,
                    do_sample=False,
                    temperature=0.1,
                    repetition_penalty=1.1
                )
            )
            
            generated_lower = generated.lower()
            food_name_lower = food_names[i].lower()
            category_lower = categories[i].lower()
            
            # Multi-criteria scoring
            score = 0.0
            
            # Food name matching (50% weight)
            food_words = [word for word in food_name_lower.split() if len(word) > 2]
            if food_words:
                name_score = sum(1 for word in food_words if word in generated_lower) / len(food_words)
                score += name_score * 0.5
            
            # Category matching (30% weight)
            category_words = [word for word in category_lower.split() if len(word) > 2]
            if category_words:
                cat_score = sum(1 for word in category_words if word in generated_lower) / len(category_words)
                score += cat_score * 0.3
            
            # Vietnamese context (20% weight)
            context_keywords = ['vietnamese', 'dish', 'food', 'cuisine']
            context_matches = sum(1 for keyword in context_keywords if keyword in generated_lower)
            context_score = min(context_matches / len(context_keywords), 1.0)
            score += context_score * 0.2
            
            # Apply diversity bonus
            score += diversity_bonus
            
            # Convert to loss
            loss = max(0.05, 1.0 - score)  # Lower minimum loss
            total_loss += loss
            valid_samples += 1
            
        except Exception as e:
            logger.warning(f"Sample {i} failed: {str(e)[:50]}")
            total_loss += 1.0
            valid_samples += 1
    
    avg_loss = total_loss / max(valid_samples, 1)
    return torch.tensor(avg_loss, requires_grad=True, device=device)

class BalancedProgressiveTrainer:
    """Fixed Balanced Progressive Trainer for Vintern"""
    
    def __init__(self, model_path="Vintern-1B-v3_5", output_dir="vintern_balanced_training"):
        self.model_path = model_path
        self.output_dir = output_dir
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"Fixed Balanced Progressive Trainer - Model: {model_path}, Device: {self.device}")
        Path(output_dir).mkdir(exist_ok=True)
        self.load_model()
    
    def load_model(self):
        """Load Vintern model"""
        logger.info("Loading Vintern v3.5...")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path, trust_remote_code=True)
        self.model = AutoModel.from_pretrained(
            self.model_path,
            trust_remote_code=True,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None,
            low_cpu_mem_usage=True
        )
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        logger.info("Model loaded successfully")
    
    def get_optimal_batch_size(self, num_categories):
        """Conservative batch size for stable training"""
        if not torch.cuda.is_available():
            return min(4, num_categories)
        
        # Start with smaller batches for stability
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory >= 5.5:
            return min(6, num_categories)  # Further reduced
        elif gpu_memory >= 4:
            return min(4, num_categories)
        else:
            return min(2, num_categories)
    
    def load_and_prepare_data(self):
        """Load from sampled dataset file"""
        logger.info("Loading sampled dataset...")

        # Try different possible file paths - prioritize smaller datasets first
        possible_paths = [
            "Small_Test_Dataset.csv",  # Small test dataset (1-2k samples)
            "Medium_Dataset.csv",      # Medium dataset (5-10k samples)
            "Final_32k_Dataset.csv",   # Full dataset
        ]
        
        df = None
        for path in possible_paths:
            try:
                if path.endswith('.csv'):
                    df = pd.read_csv(path)
                elif path.endswith('.json'):
                    df = pd.read_json(path)
                logger.info(f"Loaded data from: {path}")
                break
            except FileNotFoundError:
                continue
        
        if df is None:
            raise FileNotFoundError("Could not find sampled dataset file")
        
        # Verify expected columns
        required_cols = ['food_name', 'final_ultimate_category', 'URL']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"Required column '{col}' not found in dataset")
        
        # Add normalized_name if not present
        if 'normalized_name' not in df.columns:
            df['normalized_name'] = df['food_name'].str.lower().str.strip()
        
        logger.info(f"Dataset loaded: {len(df)} samples, {df['final_ultimate_category'].nunique()} categories")
        
        # Verify category distribution
        category_counts = df['final_ultimate_category'].value_counts()
        logger.info(f"Category distribution - Min: {category_counts.min()}, "
                   f"Max: {category_counts.max()}, Mean: {category_counts.mean():.1f}")
        
        return df
    
    def split_data(self, df):
        """Split data 70/15/15 maintaining category balance"""
        categories = df['final_ultimate_category'].unique()
        
        train_dfs = []
        val_dfs = []
        test_dfs = []
        
        for category in categories:
            cat_df = df[df['final_ultimate_category'] == category].copy()
            n = len(cat_df)
            
            # Shuffle
            cat_df = cat_df.sample(frac=1, random_state=42).reset_index(drop=True)
            
            # Split 70/15/15
            train_end = int(n * 0.7)
            val_end = int(n * 0.85)
            
            train_dfs.append(cat_df.iloc[:train_end])
            val_dfs.append(cat_df.iloc[train_end:val_end])
            test_dfs.append(cat_df.iloc[val_end:])
        
        train_df = pd.concat(train_dfs, ignore_index=True)
        val_df = pd.concat(val_dfs, ignore_index=True)
        test_df = pd.concat(test_dfs, ignore_index=True)
        
        logger.info(f"Balanced split - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
        return train_df, val_df, test_df
    
    def create_balanced_dataloaders(self, train_df, val_df, batch_size, difficulty_level=1):
        """Create FIXED balanced dataloaders with finite epochs"""
        train_dataset = CategoryBalancedDataset(train_df, self.tokenizer, difficulty_level, True)
        val_dataset = CategoryBalancedDataset(val_df, self.tokenizer, difficulty_level, False)
        
        # Calculate reasonable batch limits
        train_categories = len(train_dataset.filtered_categories)
        val_categories = len(val_dataset.filtered_categories)
        
        # Conservative batch limits for stable training
        max_train_batches = min(200, train_categories * 2)  # Max 200 batches per epoch
        max_val_batches = min(50, val_categories)           # Max 50 validation batches
        
        # Fixed balanced samplers with finite epochs
        train_sampler = FixedBalancedBatchSampler(
            train_dataset, batch_size, shuffle=True, max_batches_per_epoch=max_train_batches
        )
        val_sampler = FixedBalancedBatchSampler(
            val_dataset, batch_size, shuffle=False, max_batches_per_epoch=max_val_batches
        )
        
        train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_sampler,
            num_workers=0,
            pin_memory=True,
            collate_fn=balanced_collate_fn
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_sampler=val_sampler,
            num_workers=0,
            pin_memory=True,
            collate_fn=balanced_collate_fn
        )
        
        logger.info(f"DataLoaders created - Train batches: {len(train_loader)}, Val batches: {len(val_loader)}")
        
        return train_dataset, val_dataset, train_loader, val_loader
    
    def balanced_progressive_train(self, num_epochs_per_level=2):
        """Fixed balanced progressive training"""
        logger.info("Starting FIXED BALANCED PROGRESSIVE training...")
        start_time = time.time()
        
        # Load and prepare data
        df = self.load_and_prepare_data()
        train_df, val_df, test_df = self.split_data(df)
        
        num_categories = df['final_ultimate_category'].nunique()
        batch_size = self.get_optimal_batch_size(num_categories)
        
        logger.info(f"Training config - Categories: {num_categories}, Batch size: {batch_size}")
        
        all_results = []
        best_overall_loss = float('inf')
        
        # Progressive training through 3 levels
        for difficulty_level in [1, 2, 3]:
            logger.info(f"\n{'='*60}")
            logger.info(f"PROGRESSIVE LEVEL {difficulty_level}/3 - FIXED BALANCED SAMPLING")
            logger.info(f"{'='*60}")
            
            train_dataset, val_dataset, train_loader, val_loader = self.create_balanced_dataloaders(
                train_df, val_df, batch_size, difficulty_level
            )
            
            # FIXED: Conservative learning rate strategy without aggressive decay
            base_lr = 2e-5  # Slightly higher base LR
            lr = base_lr * (0.8 ** (difficulty_level - 1))  # Gentler reduction: 2e-5 -> 1.6e-5 -> 1.28e-5
            
            optimizer = torch.optim.AdamW(
                [p for p in self.model.parameters() if p.requires_grad],
                lr=lr,
                weight_decay=0.01,
                eps=1e-8,
                betas=(0.9, 0.999)
            )
            
            # FIXED: Constant LR scheduler to prevent LR decay to 0
            scheduler = torch.optim.lr_scheduler.ConstantLR(
                optimizer,
                factor=1.0,  # Keep LR constant
                total_iters=len(train_loader) * num_epochs_per_level
            )
            
            logger.info(f"Level {difficulty_level} - Categories: {len(train_dataset.filtered_categories)}, "
                       f"Epochs: {num_epochs_per_level}, Batch size: {batch_size}, LR: {lr:.2e}")
            logger.info(f"Expected batches per epoch: {len(train_loader)}")
            
            level_results = self._train_level(
                difficulty_level, num_epochs_per_level, train_loader, val_loader,
                optimizer, scheduler, train_dataset, val_dataset
            )
            
            all_results.append(level_results)
            
            if level_results['best_val_loss'] < best_overall_loss:
                best_overall_loss = level_results['best_val_loss']
                self.model.save_pretrained(f"{self.output_dir}/best_balanced_model")
                self.tokenizer.save_pretrained(f"{self.output_dir}/best_balanced_model")
                logger.info(f"New best model saved (loss: {best_overall_loss:.4f})")
        
        # Save final model
        self.model.save_pretrained(f"{self.output_dir}/final_balanced_model")
        self.tokenizer.save_pretrained(f"{self.output_dir}/final_balanced_model")
        
        total_time = time.time() - start_time
        
        # Final results
        final_results = {
            'fixed_balanced_progressive_training': True,
            'total_categories': num_categories,
            'difficulty_levels': 3,
            'epochs_per_level': num_epochs_per_level,
            'batch_size': batch_size,
            'train_samples': len(train_df),
            'val_samples': len(val_df),
            'test_samples': len(test_df),
            'total_time_hours': total_time / 3600,
            'best_overall_loss': float(best_overall_loss),
            'level_results': all_results
        }
        
        with open(f"{self.output_dir}/fixed_training_results.json", 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n{'='*60}")
        logger.info("FIXED BALANCED PROGRESSIVE TRAINING COMPLETED")
        logger.info(f"Total categories: {num_categories}")
        logger.info(f"Total time: {total_time/3600:.2f} hours") 
        logger.info(f"Best overall loss: {best_overall_loss:.4f}")
        logger.info(f"Models saved to: {self.output_dir}/")
        logger.info(f"{'='*60}")
        
        return final_results
    
    def _train_level(self, level, num_epochs, train_loader, val_loader,
                     optimizer, scheduler, train_dataset, val_dataset):
        """Train single difficulty level with FIXED epoch control"""
        level_train_losses = []
        level_val_losses = []
        best_level_loss = float('inf')
        
        for epoch in range(1, num_epochs + 1):
            logger.info(f"\nLevel {level}, Epoch {epoch}/{num_epochs}")
            
            # Training
            self.model.train()
            epoch_loss = 0.0
            num_batches = 0
            category_seen = set()
            
            # FIXED: Use enumerate with explicit termination
            for batch_idx, batch in enumerate(train_loader):
                # FIXED: Strict batch limit enforcement
                if batch_idx >= len(train_loader):
                    logger.info(f"Completed epoch {epoch} with {batch_idx} batches")
                    break
                    
                # Track category diversity
                batch_categories = set(batch['categories'])
                category_seen.update(batch_categories)
                
                loss = enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)
                
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()
                
                epoch_loss += loss.item()
                num_batches += 1
                
                # Memory cleanup
                del loss, batch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                if (batch_idx + 1) % 10 == 0:
                    avg_loss = epoch_loss / num_batches
                    lr = scheduler.get_last_lr()[0]
                    diversity = len(batch_categories)
                    logger.info(f"L{level} Batch {batch_idx+1}/{len(train_loader)}, "
                               f"Loss: {avg_loss:.4f}, LR: {lr:.2e}, Diversity: {diversity}")
            
            # FIXED: Calculate average loss properly
            avg_train_loss = epoch_loss / max(num_batches, 1)
            level_train_losses.append(avg_train_loss)
            
            logger.info(f"Epoch {epoch} completed: {num_batches} batches processed")
            logger.info(f"Category coverage: {len(category_seen)}/{len(train_dataset.filtered_categories)}")
            
            # Validation
            self.model.eval()
            val_loss = 0.0
            val_batches = 0
            
            with torch.no_grad():
                for val_batch_idx, batch in enumerate(val_loader):
                    # FIXED: Strict validation batch limit
                    if val_batch_idx >= len(val_loader):
                        break
                        
                    loss = enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)
                    val_loss += loss.item()
                    val_batches += 1
                    
                    del loss, batch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
            
            avg_val_loss = val_loss / max(val_batches, 1)
            level_val_losses.append(avg_val_loss)
            
            if avg_val_loss < best_level_loss:
                best_level_loss = avg_val_loss
                self.model.save_pretrained(f"{self.output_dir}/level_{level}_best")
                self.tokenizer.save_pretrained(f"{self.output_dir}/level_{level}_best")
            
            logger.info(f"Level {level} Epoch {epoch} COMPLETED:")
            logger.info(f"  Train Loss: {avg_train_loss:.4f}")
            logger.info(f"  Val Loss: {avg_val_loss:.4f}")
            logger.info(f"  Best Val Loss: {best_level_loss:.4f}")
            logger.info(f"  LR: {scheduler.get_last_lr()[0]:.2e}")
            logger.info(f"  Batches: Train={num_batches}, Val={val_batches}")
        
        return {
            'level': level,
            'num_epochs': num_epochs,
            'categories_count': len(train_dataset.filtered_categories),
            'train_losses': level_train_losses,
            'val_losses': level_val_losses,
            'best_val_loss': float(best_level_loss),
            'final_train_loss': float(level_train_losses[-1]) if level_train_losses else float('inf'),
            'final_val_loss': float(level_val_losses[-1]) if level_val_losses else float('inf')
        }

def main():
    """Main training function"""
    logger.info("="*60)
    logger.info("STARTING FIXED BALANCED PROGRESSIVE VINTERN TRAINING")
    logger.info("="*60)
    logger.info("FIXES APPLIED:")
    logger.info("✅ Fixed infinite loop in BalancedBatchSampler")
    logger.info("✅ Fixed LR scheduler decay to 0 (using ConstantLR)")
    logger.info("✅ Added strict epoch termination")
    logger.info("✅ Conservative batch limits for stable training")
    logger.info("✅ Better memory management")
    logger.info("="*60)
    
    trainer = BalancedProgressiveTrainer(
        model_path="Vintern-1B-v3_5",
        output_dir="vintern_fixed_balanced"
    )
    
    results = trainer.balanced_progressive_train(num_epochs_per_level=2)
    
    print("\n" + "="*60)
    print("🎉 FIXED BALANCED PROGRESSIVE TRAINING COMPLETED!")
    print("="*60)
    print(f"✅ Total categories: {results['total_categories']}")
    print(f"✅ Best validation loss: {results['best_overall_loss']:.4f}")
    print(f"✅ Total training time: {results['total_time_hours']:.2f} hours")
    print(f"✅ Batch size: {results['batch_size']} (optimized for stability)")
    print(f"✅ Data split: Train {results['train_samples']}, Val {results['val_samples']}, Test {results['test_samples']}")
    print(f"✅ Models saved to: vintern_fixed_balanced/")
    print("="*60)
    print("KEY IMPROVEMENTS:")
    print("- Learning rate stays constant (no decay to 0)")
    print("- Finite epochs with proper termination")
    print("- Conservative batch limits for stability")  
    print("- Better error handling and logging")
    print("="*60)

if __name__ == "__main__":
    main()