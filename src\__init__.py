"""
VLM Model Package

This package contains the main source code for the VLM (Vision Language Model) project.

Modules:
- preprocessing: Data preprocessing utilities
- finetune_multi_lora: Multi-LoRA finetuning implementation
- finetune_progressive: Progressive method finetuning
- test_methods: Testing methods for VinternV3.5 model
- ui: User interface for VLM
"""

__version__ = "1.0.0"
__author__ = "VLM Team"

# Import main modules for easy access
from . import preprocessing
from . import finetune_multi_lora
from . import finetune_progressive
from . import test_methods
from . import ui
