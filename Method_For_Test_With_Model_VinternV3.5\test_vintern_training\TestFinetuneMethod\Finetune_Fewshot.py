#!/usr/bin/env python3
"""
APPROACH 5: Few-Shot Learning
Sử dụng in-context examples để cải thiện performance
"""

import os
import torch
import pandas as pd
import logging
import random
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FewShotDataset(Dataset):
    """
    Dataset cho Few-Shot Learning
    Format: Examples + Query + Response
    """
    def __init__(self, df, tokenizer, max_length=200, num_examples=3):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.num_examples = num_examples
        self.few_shot_data = self.create_few_shot_examples()
        
        logger.info(f"Tạo FewShotDataset với {len(self.few_shot_data)} few-shot examples")
    
    def create_few_shot_examples(self):
        """Tạo few-shot examples với in-context learning"""
        
        # Tạo pool of good examples
        food_examples = []
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            if len(food_name) >= 3 and len(food_name.split()) <= 4:
                food_examples.append(food_name)
        
        few_shot_data = []
        
        for _, row in self.df.iterrows():
            target_food = row['food_name'].strip().lower()
            
            if len(target_food) < 3:
                continue
            
            # Chọn random examples (không bao gồm target)
            available_examples = [f for f in food_examples if f != target_food]
            if len(available_examples) < self.num_examples:
                continue
            
            selected_examples = random.sample(available_examples, self.num_examples)
            
            # Tạo few-shot prompt
            examples_text = ""
            for i, example in enumerate(selected_examples, 1):
                examples_text += f"Example {i}:\n"
                examples_text += f"Input: Nhận diện món ăn\n"
                examples_text += f"Output: {example}\n\n"
            
            # Query và Response
            query_text = f"Query:\nInput: Nhận diện món ăn\nOutput:"
            full_text = f"### Few-Shot Learning ###\n\n{examples_text}{query_text} {target_food}"
            
            few_shot_data.append({
                'examples': selected_examples,
                'target': target_food,
                'full_text': full_text
            })
        
        return few_shot_data
    
    def __len__(self):
        return len(self.few_shot_data)
    
    def __getitem__(self, idx):
        item = self.few_shot_data[idx]
        text = item['full_text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'target': item['target']
        }

def setup_fewshot_lora(model):
    """Setup LoRA cho Few-Shot Learning"""
    logger.info("=== THIẾT LẬP FEW-SHOT LORA ===")
    
    for param in model.parameters():
        param.requires_grad = False
    
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        # LoRA config cho few-shot (focus vào context understanding)
        lora_config = LoraConfig(
            r=40,                    # Rank trung bình cao
            lora_alpha=80,           # Alpha cao cho few-shot
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # Attention quan trọng cho context
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.02,       # Dropout rất thấp
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        model.language_model = get_peft_model(llm, lora_config)
        logger.info("✅ Few-Shot LoRA applied")
        model.language_model.print_trainable_parameters()
    
    return model

def train_fewshot_model(model, train_dataloader, device, num_epochs=1):
    """Training Few-Shot Model"""
    logger.info("=== BẮT ĐẦU FEW-SHOT TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.AdamW(trainable_params, lr=1.5e-4)  # Learning rate vừa phải
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    max_steps = 70  # Vừa đủ cho few-shot
    
    for epoch in range(num_epochs):
        logger.info(f"--- FEW-SHOT EPOCH {epoch + 1}/{num_epochs} ---")
        
        for batch in tqdm(train_dataloader, desc=f"Few-Shot Training"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                
                optimizer.zero_grad()
                loss.backward()
                
                # Gradient clipping cho stability
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=0.5)
                
                optimizer.step()
                
                step += 1
                
                if step % 12 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps} - Loss: {loss.item():.4f} - Avg Loss: {avg_loss:.4f}")
                
                training_history.append({
                    'step': step,
                    'loss': loss.item(),
                    'avg_loss': total_loss / step
                })
                
            except Exception as e:
                logger.error(f"Few-shot training error: {str(e)}")
                step += 1
                continue
        
        if step >= max_steps:
            break
    
    logger.info("✅ Few-Shot training completed!")
    return model, training_history

def test_fewshot_model(model, tokenizer, device):
    """Test Few-Shot Model với different example sets"""
    logger.info("=== TESTING FEW-SHOT MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test cases với different few-shot examples
    test_cases = [
        {
            'examples': ['cà phê sữa', 'trà đá', 'nước cam'],
            'description': 'Đồ uống examples'
        },
        {
            'examples': ['bánh mì', 'phở bò', 'cơm tấm'],
            'description': 'Món ăn examples'
        },
        {
            'examples': ['bánh flan', 'chè đậu', 'kem vanilla'],
            'description': 'Tráng miệng examples'
        }
    ]
    
    test_results = []
    
    for test_case in test_cases:
        examples = test_case['examples']
        description = test_case['description']
        
        # Tạo few-shot prompt
        examples_text = ""
        for i, example in enumerate(examples, 1):
            examples_text += f"Example {i}:\n"
            examples_text += f"Input: Nhận diện món ăn\n"
            examples_text += f"Output: {example}\n\n"
        
        query_text = f"Query:\nInput: Nhận diện món ăn\nOutput:"
        full_prompt = f"### Few-Shot Learning ###\n\n{examples_text}{query_text}"
        
        try:
            inputs = tokenizer(full_prompt, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 20,
                    temperature=0.5,  # Thấp hơn cho consistency
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(full_prompt):].strip()
            
            if generated:
                generated = generated.split('\n')[0].strip()
            
            test_results.append({
                'examples': examples,
                'description': description,
                'generated': generated
            })
            
            logger.info(f"{description}: '{generated}'")
            
        except Exception as e:
            logger.warning(f"Generation failed for {description}: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 5: FEW-SHOT LEARNING")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach5_fewshot_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Setup Few-Shot LoRA
    model = setup_fewshot_lora(model)
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(250).dropna(subset=['food_name'])  # Medium size for few-shot
    
    # Create few-shot dataset
    train_dataset = FewShotDataset(train_df, tokenizer, max_length=200, num_examples=3)
    train_dataloader = DataLoader(train_dataset, batch_size=2, shuffle=True)  # Smaller batch
    
    logger.info(f"Few-shot examples: {len(train_dataset)}")
    
    # Training
    model, history = train_fewshot_model(model, train_dataloader, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_fewshot_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'Few-Shot Learning',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'fewshot_examples': len(train_dataset)
    }
    
    with open('output_fewshot.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 5: FEW-SHOT LEARNING RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"Few-shot examples: {results['fewshot_examples']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']}: Loss={entry['loss']:.4f}, Avg Loss={entry['avg_loss']:.4f}\n")
        
        f.write("\nFew-Shot Test Results:\n")
        for result in test_results:
            f.write(f"{result['description']}: '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 5 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_fewshot.txt")

if __name__ == "__main__":
    main()
