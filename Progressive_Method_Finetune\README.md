## Progressive Fine-tuning — <PERSON> chế độ: Local (sample) & Server (full) [ĐỒNG BỘ HÓA]

Mục tiêu: Huấn luyện theo curriculum (dễ→khó) với sampler cân bằng theo category, bảo toàn đa dạng và ổn định tối ưu.

**🔄 ĐÃ ĐỒNG BỘ HÓA THUẬT TOÁN LOCAL ↔ SERVER** để đảm bảo kết quả nhất quán và ổn định training.

Mã nguồn chính:
- Local: FinetuneWithProgressInLocal/FinetuneWithProgressiveMethod.py
- Server (L4 24GB): FinetuneWithProgressiveInServer/Finetune_L4_Progressive_method.py
- Test & Verify: test_server_stability.py, compare_local_server.py

---

## 1) Local progressive fine-tune (từ tập sample)
Tập dữ liệu: Balanced sample từ bước Take Sample (training_data.json hoặc balanced_training_dataset.csv, các cột: food_name, final_ultimate_category, URL, ...)

Đặc điểm chính (đọc từ code):
- CategoryBalancedDataset: tổ chức dữ liệu theo category; generate instruction/response theo level 1→3
- BalancedBatchSampler: mỗi batch lấy đều từ nhiều category, tránh collapse
- 3 level độ khó; LR giảm nhẹ theo level (2e-5 → 1.6e-5 → 1.28e-5)
- Kiểm soát số batch/epoch để tránh vòng lặp vô hạn, cleanup bộ nhớ, logging chi tiết

Luồng chính:
1) Load sampled dataset (Final_32k_Dataset.csv hoặc balanced_training_dataset.csv)
2) Split 70/15/15 theo từng category (đảm bảo cân bằng)
3) Tạo DataLoader cân bằng + collate_fn phù hợp
4) Train theo từng level, validate sau mỗi epoch
5) Lưu kết quả và log

Chạy nhanh (ví dụ):
- cd Progressive_Method_Finetune/FinetuneWithProgressInLocal
- python FinetuneWithProgressiveMethod.py

Tham số chính trong code:
- Batch size tự động theo số category (bảo toàn đa dạng batch)
- AdamW (betas 0.9/0.999), weight_decay 0.01
- ConstantLR / lịch LR bảo thủ (không decay về 0)

Lưu ý kỹ thuật:
- Đầu vào ảnh: transform Resize 448, Normalize theo ImageNet
- Model.chat() dùng để sinh văn bản so sánh và tính điểm khớp nhiều tiêu chí (tên món, category, context VN) → chuyển đổi thành loss differentiable proxy (trong code: chuyển điểm → loss)
- Đây là proxy loss để tận dụng giao diện chat của Vintern; theo dõi stability và xem xét thay thế bằng supervised objective nếu có annotation chuẩn

---

## 2) Server L4 progressive fine-tune (full 700k, ĐỒNG BỘ VỚI LOCAL)
Tập dữ liệu: FINAL_ULTIMATE_COMPLETE_DATASET.csv (805,508 items sau hợp nhất)

**🔧 CẢI TIẾN ĐỒNG BỘ HÓA:**
- **FixedBalancedBatchSampler**: Finite epochs, không infinite loop như trước
- **Learning rate**: Đồng bộ với local (2e-5 * 0.8^(level-1))
- **ConstantLR scheduler**: Không decay về 0, giữ LR ổn định
- **Loss computation**: Cùng scoring criteria với local
- **Training loop**: Strict batch limits, category diversity tracking

**🚀 TÍNH NĂNG L4 SERVER:**
- bfloat16, flash attention 2, device_map="auto" cho 24GB VRAM
- Image caching (image_cache/*.pt) cho 700k dataset
- Batch size tối ưu: tới 48 (tự động điều chỉnh theo VRAM)
- Enhanced error recovery: CUDA OOM handling, URL retry logic
- Checkpoint/Resume: mỗi epoch, giữ 3 checkpoint/level, versioning
- URL validation sampling: kiểm tra 1% URLs để ước tính chất lượng

**📋 QUY TRÌNH TRAINING:**
1) Load full dataset + URL validation sampling
2) Split 70/15/15 balanced theo category
3) Auto batch size cho L4 (24GB → ~48 batch size)
4) 3 difficulty levels × N epochs (mặc định 3)
5) Mỗi epoch: train (finite) → validate → checkpoint → early stopping
6) Model versioning: level_X_best, level_X_final, latest_*

**⚡ CHẠY NHANH:**
```bash
cd Progressive_Method_Finetune/FinetuneWithProgressiveInServer

# Training mới
python Finetune_L4_Progressive_method.py --epochs 3 --model_path Vintern-1B-v3_5 --output_dir vintern_l4_server_final

# Resume từ checkpoint
python Finetune_L4_Progressive_method.py --resume path/to/checkpoint_level_2_epoch_1.pt

# Test stability trước khi chạy production
python test_server_stability.py
```

**🔧 THAM SỐ ĐỒNG BỘ:**
- Learning rate: 2e-5 * (0.8 ** (level-1)) [ĐỒNG BỘ LOCAL]
- Optimizer: AdamW betas=(0.9,0.999), weight_decay=0.01 [ĐỒNG BỘ LOCAL]
- Scheduler: ConstantLR(factor=1.0) [ĐỒNG BỘ LOCAL]
- Loss: max_new_tokens=80, min_loss=0.05 [ĐỒNG BỘ LOCAL]

**⚠️ PRODUCTION CHECKLIST:**
- ✅ Chạy test_server_stability.py trước
- ✅ Kiểm tra VRAM available (cần ~20GB cho batch 48)
- ✅ Đảm bảo FINAL_ULTIMATE_COMPLETE_DATASET.csv có sẵn
- ✅ Set up checkpoint directory với đủ disk space
- ✅ Monitor GPU temperature và memory usage

---

## 3) Best practices & đánh giá
- Curriculum: bắt đầu từ category/chuỗi đơn giản (Level 1) để ổn định, sau đó tăng độ khó
- Balance: duy trì đa dạng category mỗi batch giúp model tổng quát tốt hơn
- Proxy loss: nếu có ground-truth caption/answer, thay bằng cross-entropy supervised chuẩn sẽ ổn định hơn
- Kiểm tra overfit: theo dõi val loss; dùng augment nhẹ cho ảnh nếu cần
- Kiểm tra bias: do phân phối category lệch, cân nhắc class weighting hoặc focal-type adjustments

Đầu ra mong đợi:
- training_results.json (server) với thống kê thời gian, best val loss, đường loss per level
- Model checkpoints trong output_dir/checkpoints/ và phiên bản hóa latest_*

---

## 4) Liên kết
- Preprocessing_Data/README.md — pipeline dữ liệu và cách tạo sample
- UI_For_VLM — đưa model đã fine-tune vào UI để test
