#!/usr/bin/env python3
"""
APPROACH 6: Hybrid Training
Kết hợp LoRA + selective full fine-tuning cho critical layers
"""

import os
import torch
import pandas as pd
import logging
import random
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HybridDataset(Dataset):
    """
    Dataset cho Hybrid Training
    Kết hợp instruction + multi-task format
    """
    def __init__(self, df, tokenizer, max_length=180):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.hybrid_data = self.create_hybrid_examples()
        
        logger.info(f"Tạo HybridDataset với {len(self.hybrid_data)} hybrid examples")
    
    def create_hybrid_examples(self):
        """Tạo hybrid examples kết hợp nhiều format"""
        
        formats = [
            # Format 1: Instruction style
            ("### Instruction: Nhận diện món ăn:\n### Response: {food_name}", "instruction"),
            
            # Format 2: Direct style  
            ("Food: {food_name}", "direct"),
            
            # Format 3: Question style
            ("Q: Tên món ăn này là gì?\nA: {food_name}", "question"),
            
            # Format 4: Context style
            ("Trong menu có món: {food_name}", "context"),
            
            # Format 5: Description style
            ("Món ăn được gọi là: {food_name}", "description")
        ]
        
        hybrid_data = []
        
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            
            if len(food_name) < 3:
                continue
            
            # Tạo 2-3 examples với format khác nhau cho mỗi food item
            selected_formats = random.sample(formats, min(2, len(formats)))
            
            for format_template, format_type in selected_formats:
                text = format_template.format(food_name=food_name)
                
                hybrid_data.append({
                    'food_name': food_name,
                    'format_type': format_type,
                    'text': text
                })
        
        # Shuffle để mix formats
        random.shuffle(hybrid_data)
        return hybrid_data
    
    def __len__(self):
        return len(self.hybrid_data)
    
    def __getitem__(self, idx):
        item = self.hybrid_data[idx]
        text = item['text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'format_type': item['format_type']
        }

def setup_hybrid_training(model):
    """
    Setup Hybrid Training:
    - LoRA cho most layers
    - Full fine-tuning cho critical layers (embedding, final layers)
    """
    logger.info("=== THIẾT LẬP HYBRID TRAINING ===")
    
    if not hasattr(model, 'language_model'):
        logger.error("Model không có language_model component")
        return model
    
    llm = model.language_model
    
    # Step 1: Freeze toàn bộ model trước
    for param in model.parameters():
        param.requires_grad = False
    
    # Step 2: Unfreeze critical layers cho full fine-tuning
    critical_layers = []
    
    # Embedding layers
    if hasattr(llm, 'model') and hasattr(llm.model, 'embed_tokens'):
        for param in llm.model.embed_tokens.parameters():
            param.requires_grad = True
        critical_layers.append("embed_tokens")
    
    # Final layers (last 2 transformer layers)
    if hasattr(llm, 'model') and hasattr(llm.model, 'layers'):
        num_layers = len(llm.model.layers)
        for i in range(max(0, num_layers-2), num_layers):  # Last 2 layers
            for param in llm.model.layers[i].parameters():
                param.requires_grad = True
            critical_layers.append(f"layer_{i}")
    
    # LM head
    if hasattr(llm, 'lm_head'):
        for param in llm.lm_head.parameters():
            param.requires_grad = True
        critical_layers.append("lm_head")
    
    logger.info(f"✅ Critical layers for full fine-tuning: {critical_layers}")
    
    # Step 3: Apply LoRA cho remaining layers
    lora_config = LoraConfig(
        r=24,                    # Medium rank
        lora_alpha=48,           # Medium alpha
        target_modules=[
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj"
        ],
        lora_dropout=0.04,
        bias="none",
        task_type=TaskType.CAUSAL_LM,
    )
    
    # Apply LoRA (sẽ skip các layers đã unfreeze)
    model.language_model = get_peft_model(llm, lora_config)
    
    logger.info("✅ Hybrid LoRA applied")
    model.language_model.print_trainable_parameters()
    
    # Count total trainable parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"📊 Hybrid Training Stats:")
    logger.info(f"   Total parameters: {total_params:,}")
    logger.info(f"   Trainable parameters: {trainable_params:,}")
    logger.info(f"   Trainable ratio: {trainable_params/total_params*100:.2f}%")
    
    return model

def train_hybrid_model(model, train_dataloader, device, num_epochs=1):
    """Training Hybrid Model"""
    logger.info("=== BẮT ĐẦU HYBRID TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    # Different learning rates for different components
    optimizer = torch.optim.AdamW([
        {'params': [p for n, p in model.named_parameters() 
                   if p.requires_grad and ('embed' in n or 'lm_head' in n)], 
         'lr': 5e-5},  # Lower LR for critical layers
        {'params': [p for n, p in model.named_parameters() 
                   if p.requires_grad and not ('embed' in n or 'lm_head' in n)], 
         'lr': 2e-4}   # Higher LR for LoRA layers
    ])
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    max_steps = 90  # More steps for hybrid
    
    format_losses = {'instruction': [], 'direct': [], 'question': [], 'context': [], 'description': []}
    
    for epoch in range(num_epochs):
        logger.info(f"--- HYBRID EPOCH {epoch + 1}/{num_epochs} ---")
        
        for batch in tqdm(train_dataloader, desc=f"Hybrid Training"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            format_types = batch['format_type']
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                
                # Track loss per format type
                for i, format_type in enumerate(format_types):
                    if format_type in format_losses:
                        format_losses[format_type].append(loss.item())
                
                optimizer.zero_grad()
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=1.0)
                
                optimizer.step()
                
                step += 1
                
                if step % 18 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps} - Loss: {loss.item():.4f} - Avg Loss: {avg_loss:.4f}")
                    
                    # Log format-specific losses
                    for format_type, losses in format_losses.items():
                        if losses:
                            avg_format_loss = sum(losses[-5:]) / len(losses[-5:])  # Last 5
                            logger.info(f"  {format_type}: {avg_format_loss:.4f}")
                
                training_history.append({
                    'step': step,
                    'loss': loss.item(),
                    'avg_loss': total_loss / step
                })
                
            except Exception as e:
                logger.error(f"Hybrid training error: {str(e)}")
                step += 1
                continue
        
        if step >= max_steps:
            break
    
    logger.info("✅ Hybrid training completed!")
    return model, training_history

def test_hybrid_model(model, tokenizer, device):
    """Test Hybrid Model với different formats"""
    logger.info("=== TESTING HYBRID MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test với các format khác nhau
    test_formats = [
        "### Instruction: Nhận diện món ăn:\n### Response:",
        "Food:",
        "Q: Tên món ăn này là gì?\nA:",
        "Trong menu có món:",
        "Món ăn được gọi là:"
    ]
    
    test_results = []
    
    for test_format in test_formats:
        try:
            inputs = tokenizer(test_format, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 20,
                    temperature=0.6,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(test_format):].strip()
            
            if generated:
                generated = generated.split('\n')[0].strip()
            
            test_results.append({
                'format': test_format.replace('\n', ' '),
                'generated': generated
            })
            
            logger.info(f"'{test_format.replace(chr(10), ' ')}' → '{generated}'")
            
        except Exception as e:
            logger.warning(f"Generation failed: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 6: HYBRID TRAINING")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach6_hybrid_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Setup Hybrid Training
    model = setup_hybrid_training(model)
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(220).dropna(subset=['food_name'])  # Medium size
    
    # Create hybrid dataset
    train_dataset = HybridDataset(train_df, tokenizer)
    train_dataloader = DataLoader(train_dataset, batch_size=2, shuffle=True)
    
    logger.info(f"Hybrid examples: {len(train_dataset)}")
    
    # Training
    model, history = train_hybrid_model(model, train_dataloader, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_hybrid_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'Hybrid Training',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'hybrid_examples': len(train_dataset)
    }
    
    with open('output_hybrid.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 6: HYBRID TRAINING RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"Hybrid examples: {results['hybrid_examples']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']}: Loss={entry['loss']:.4f}, Avg Loss={entry['avg_loss']:.4f}\n")
        
        f.write("\nHybrid Test Results:\n")
        for result in test_results:
            f.write(f"'{result['format']}' → '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 6 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_hybrid.txt")

if __name__ == "__main__":
    main()
