#!/usr/bin/env python3
"""
Approach 2: LoRA trên LLM component
Sử dụng LoRA để finetune hi<PERSON><PERSON> quả chỉ phần LLM của VLM
"""

import os
import torch
import pandas as pd
import logging
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FoodDataset(Dataset):
    def __init__(self, df, tokenizer, max_length=64):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.df)
    
    def __getitem__(self, idx):
        food_name = self.df.iloc[idx]['food_name']
        
        # Format đơn giản cho LoRA training
        text = f"Food: {food_name}"
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze()
        }

def setup_lora_for_llm(model):
    """Setup LoRA chỉ cho Language Model component"""
    logger.info("Setting up LoRA for Language Model...")
    
    # Freeze toàn bộ model trước
    for param in model.parameters():
        param.requires_grad = False
    
    # Chỉ apply LoRA cho language model
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        # LoRA config
        lora_config = LoraConfig(
            r=16,  # rank
            lora_alpha=32,  # scaling factor
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",  # attention
                "gate_proj", "up_proj", "down_proj"      # MLP
            ],
            lora_dropout=0.1,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        # Apply LoRA to language model
        model.language_model = get_peft_model(llm, lora_config)
        
        logger.info("LoRA applied to language model")
        model.language_model.print_trainable_parameters()
    
    return model

def train_with_lora(model, train_dataloader, device, num_epochs=1, learning_rate=1e-4):
    """Training với LoRA"""
    
    # Chỉ optimize LoRA parameters
    trainable_params = []
    for name, param in model.named_parameters():
        if param.requires_grad:
            trainable_params.append(param)
    
    logger.info(f"Number of trainable parameters: {len(trainable_params)}")
    
    optimizer = torch.optim.AdamW(trainable_params, lr=learning_rate)
    
    model.train()
    total_loss = 0
    step = 0
    max_steps = 30  # Giảm steps để test nhanh
    
    logger.info(f"Starting LoRA training for {num_epochs} epoch(s), max {max_steps} steps...")
    
    for epoch in range(num_epochs):
        for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1}"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            try:
                # Thử approach đơn giản: chỉ train language model trực tiếp
                # Bỏ qua VLM wrapper, train trực tiếp LLM
                llm = model.language_model
                
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                step += 1
                
                if step % 5 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps}, Loss: {loss.item():.4f}, Avg Loss: {avg_loss:.4f}")
                
            except Exception as e:
                logger.error(f"Error in training step {step}: {str(e)}")
                step += 1  # Skip this batch
                continue
        
        if step >= max_steps:
            break
    
    return model

def test_lora_generation(model, tokenizer, device):
    """Test generation với LoRA model"""
    logger.info("Testing LoRA generation...")
    
    model.eval()
    test_prompts = [
        "Food:",
        "Food: coffee",
        "Food: banh mi"
    ]
    
    # Test trực tiếp với language model
    llm = model.language_model
    
    for prompt in test_prompts:
        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 15,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            logger.info(f"Prompt: '{prompt}' -> Generated: '{generated}'")
            
        except Exception as e:
            logger.warning(f"Generation failed for '{prompt}': {str(e)}")

def main():
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach2_lora_output"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    logger.info("Loading model...")
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    logger.info(f"Using device: {device}")
    
    # Setup LoRA
    model = setup_lora_for_llm(model)
    
    # Load data
    logger.info("Loading training data...")
    df = pd.read_csv(train_data_path)
    df_small = df.head(100).copy()  # Smaller subset for LoRA
    df_small = df_small.dropna(subset=['food_name'])
    
    # Create dataset
    train_dataset = FoodDataset(df_small, tokenizer, max_length=64)
    train_dataloader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    
    logger.info(f"Training on {len(df_small)} samples")
    
    # Train with LoRA
    model = train_with_lora(model, train_dataloader, device, num_epochs=1)
    
    # Save LoRA model
    logger.info("Saving LoRA model...")
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test generation
    test_lora_generation(model, tokenizer, device)
    
    logger.info("Approach 2 (LoRA) completed!")

if __name__ == "__main__":
    main()
