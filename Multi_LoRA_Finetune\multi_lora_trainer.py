#!/usr/bin/env python3
"""
Multi-LoRA Trainer cho Food + Harmful Detection
- R<PERSON>t kinh nghiệm từ Progressive training issues
- Stable training với proper batch handling
- Multi-task loss balancing
- Advanced monitoring và checkpointing
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import time
import json
import numpy as np
from pathlib import Path
from collections import defaultdict
import gc

from multi_lora_architecture import create_multi_lora_model, MultiTaskLoss
from multi_task_dataset_pipeline import create_multi_task_dataloaders

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('multi_lora_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MultiLoRATrainer:
    """Multi-LoRA trainer với lessons learned từ Progressive training"""
    
    def __init__(self, model_path="Vintern-1B-v3_5", output_dir="multi_lora_results", device='cuda'):
        self.model_path = model_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.device = device
        
        # Training state
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Metrics tracking
        self.training_history = {
            'train_losses': [],
            'val_losses': [],
            'task_losses': defaultdict(list),
            'accuracies': defaultdict(list)
        }
        
        logger.info(f"Multi-LoRA Trainer initialized")
        logger.info(f"Output directory: {self.output_dir}")
    
    def setup_model_and_data(self, food_data_path, harmful_data_path, 
                           food_rank=64, harmful_rank=32, batch_size=8):
        """Setup model và data loaders"""
        logger.info("Setting up model and data...")
        
        # Load food categories từ dataset
        import pandas as pd
        food_df = pd.read_csv(food_data_path)
        food_categories = sorted(food_df['final_ultimate_category'].unique().tolist())
        food_categories = [cat for cat in food_categories if cat != 'Chưa phân loại']
        
        logger.info(f"Food categories: {len(food_categories)}")
        
        # Create model
        self.model = create_multi_lora_model(
            self.model_path, food_categories, 
            food_rank=food_rank, harmful_rank=harmful_rank, 
            device=self.device
        )
        
        # Create data loaders
        self.train_loader, self.val_loader, self.test_loader = create_multi_task_dataloaders(
            food_data_path, harmful_data_path, None,  # tokenizer not needed for this setup
            batch_size=batch_size, task_balance_ratio=0.6  # 60% food, 40% harmful
        )
        
        # Setup loss function
        self.criterion = MultiTaskLoss(
            food_weight=1.0, 
            harmful_weight=1.2,  # Slightly higher weight for harmful detection
            focal_gamma=2.0,
            label_smoothing=0.1
        ).to(self.device)
        
        logger.info("Model and data setup completed")
        return food_categories
    
    def setup_optimizer_and_scheduler(self, learning_rate=2e-4, num_epochs=10):
        """Setup optimizer và scheduler với lessons learned"""
        logger.info("Setting up optimizer and scheduler...")
        
        # Separate learning rates cho different components
        lora_params = []
        head_params = []
        
        # LoRA parameters (lower LR)
        for param in self.model.food_lora.parameters():
            if param.requires_grad:
                lora_params.append(param)
        
        for param in self.model.harmful_lora.parameters():
            if param.requires_grad:
                lora_params.append(param)
        
        # Classification head parameters (higher LR)
        for param in self.model.food_head.parameters():
            head_params.append(param)
        
        for param in self.model.harmful_head.parameters():
            head_params.append(param)
        
        for param in self.model.task_router.parameters():
            head_params.append(param)
        
        # Optimizer với different learning rates
        self.optimizer = optim.AdamW([
            {'params': lora_params, 'lr': learning_rate, 'weight_decay': 0.01},
            {'params': head_params, 'lr': learning_rate * 2, 'weight_decay': 0.005}  # Higher LR for heads
        ], eps=1e-8, betas=(0.9, 0.999))
        
        # Scheduler: OneCycleLR cho stable training
        total_steps = len(self.train_loader) * num_epochs
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=[learning_rate, learning_rate * 2],
            total_steps=total_steps,
            pct_start=0.1,  # 10% warmup
            anneal_strategy='cos',
            div_factor=10,  # Initial LR = max_lr / div_factor
            final_div_factor=100  # Final LR = max_lr / final_div_factor
        )
        
        logger.info(f"Optimizer setup: LoRA LR={learning_rate:.2e}, Head LR={learning_rate*2:.2e}")
        logger.info(f"Scheduler: OneCycleLR with {total_steps} total steps")
    
    def train_epoch(self, epoch, num_epochs):
        """Train single epoch với proper error handling"""
        self.model.train()
        epoch_loss = 0.0
        epoch_task_losses = defaultdict(float)
        epoch_accuracies = defaultdict(list)
        num_batches = 0
        
        logger.info(f"\nEpoch {epoch}/{num_epochs}")
        epoch_start_time = time.time()
        
        for batch_idx, batch in enumerate(self.train_loader):
            try:
                # Move to device
                images = batch['images'].to(self.device, dtype=torch.bfloat16)
                task_types = batch['task_types']
                labels = batch['labels']
                
                # Forward pass
                logits = self.model(images, task_types)
                
                # Compute loss
                loss, task_losses = self.criterion(
                    logits, labels, task_types, self.model.food_categories
                )
                
                # Backward pass
                self.optimizer.zero_grad()
                loss.backward()
                
                # Gradient clipping (lesson từ Progressive training)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                self.scheduler.step()
                
                # Update metrics
                epoch_loss += loss.item()
                for task, task_loss in task_losses.items():
                    epoch_task_losses[task] += task_loss
                
                # Compute accuracies
                accuracies = self._compute_batch_accuracies(logits, labels, task_types)
                for task, acc in accuracies.items():
                    epoch_accuracies[task].extend(acc)
                
                num_batches += 1
                
                # Memory cleanup (lesson từ Progressive training)
                del loss, logits, images
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                # Progress logging
                if (batch_idx + 1) % 20 == 0:
                    avg_loss = epoch_loss / num_batches
                    lr = self.scheduler.get_last_lr()[0]
                    elapsed = time.time() - epoch_start_time
                    
                    logger.info(f"Epoch {epoch} Batch {batch_idx+1}/{len(self.train_loader)}, "
                               f"Loss: {avg_loss:.4f}, LR: {lr:.2e}, Time: {elapsed:.1f}s")
                    
                    # Log task-specific losses
                    for task, task_loss in epoch_task_losses.items():
                        avg_task_loss = task_loss / num_batches
                        logger.info(f"  {task}_loss: {avg_task_loss:.4f}")
                
            except Exception as e:
                logger.error(f"Error in batch {batch_idx}: {e}")
                # Skip problematic batch
                continue
        
        # Compute epoch averages
        avg_epoch_loss = epoch_loss / max(num_batches, 1)
        avg_task_losses = {task: loss / max(num_batches, 1) 
                          for task, loss in epoch_task_losses.items()}
        avg_accuracies = {task: np.mean(accs) if accs else 0.0 
                         for task, accs in epoch_accuracies.items()}
        
        return avg_epoch_loss, avg_task_losses, avg_accuracies
    
    def validate_epoch(self, epoch):
        """Validation epoch"""
        self.model.eval()
        val_loss = 0.0
        val_task_losses = defaultdict(float)
        val_accuracies = defaultdict(list)
        num_batches = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(self.val_loader):
                try:
                    # Move to device
                    images = batch['images'].to(self.device, dtype=torch.bfloat16)
                    task_types = batch['task_types']
                    labels = batch['labels']
                    
                    # Forward pass
                    logits = self.model(images, task_types)
                    
                    # Compute loss
                    loss, task_losses = self.criterion(
                        logits, labels, task_types, self.model.food_categories
                    )
                    
                    # Update metrics
                    val_loss += loss.item()
                    for task, task_loss in task_losses.items():
                        val_task_losses[task] += task_loss
                    
                    # Compute accuracies
                    accuracies = self._compute_batch_accuracies(logits, labels, task_types)
                    for task, acc in accuracies.items():
                        val_accuracies[task].extend(acc)
                    
                    num_batches += 1
                    
                    # Memory cleanup
                    del loss, logits, images
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                
                except Exception as e:
                    logger.error(f"Error in validation batch {batch_idx}: {e}")
                    continue
        
        # Compute averages
        avg_val_loss = val_loss / max(num_batches, 1)
        avg_task_losses = {task: loss / max(num_batches, 1) 
                          for task, loss in val_task_losses.items()}
        avg_accuracies = {task: np.mean(accs) if accs else 0.0 
                         for task, accs in val_accuracies.items()}
        
        return avg_val_loss, avg_task_losses, avg_accuracies
    
    def _compute_batch_accuracies(self, logits, labels, task_types):
        """Compute accuracies cho each task trong batch"""
        accuracies = defaultdict(list)
        
        for i, task_type in enumerate(task_types):
            if task_type == 'food_classification':
                task_logits = logits[i, :len(self.model.food_categories)]
                pred_idx = torch.argmax(task_logits).item()
                true_label = labels[i]
                pred_label = self.model.food_categories[pred_idx]
                accuracies['food'].append(1.0 if pred_label == true_label else 0.0)
            
            else:  # harmful_detection
                task_logits = logits[i, :2]
                pred_idx = torch.argmax(task_logits).item()
                true_label = labels[i]
                pred_label = 'safe' if pred_idx == 0 else 'harmful'
                accuracies['harmful'].append(1.0 if pred_label == true_label else 0.0)
        
        return accuracies
    
    def save_checkpoint(self, epoch, train_loss, val_loss, is_best=False):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_loss': train_loss,
            'val_loss': val_loss,
            'training_history': self.training_history
        }
        
        # Save regular checkpoint
        checkpoint_path = self.output_dir / f"checkpoint_epoch_{epoch}.pt"
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = self.output_dir / "best_model.pt"
            torch.save(checkpoint, best_path)
            logger.info(f"Best model saved at epoch {epoch}")
        
        logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def train(self, food_data_path, harmful_data_path, 
              num_epochs=10, learning_rate=2e-4, batch_size=8,
              food_rank=64, harmful_rank=32):
        """Main training function"""
        logger.info("="*60)
        logger.info("STARTING MULTI-LORA TRAINING")
        logger.info("="*60)
        logger.info(f"Food rank: {food_rank}, Harmful rank: {harmful_rank}")
        logger.info(f"Epochs: {num_epochs}, LR: {learning_rate}, Batch size: {batch_size}")
        logger.info("="*60)
        
        start_time = time.time()
        
        # Setup
        food_categories = self.setup_model_and_data(
            food_data_path, harmful_data_path, food_rank, harmful_rank, batch_size
        )
        self.setup_optimizer_and_scheduler(learning_rate, num_epochs)
        
        # Training loop
        best_val_loss = float('inf')
        
        for epoch in range(1, num_epochs + 1):
            # Train
            train_loss, train_task_losses, train_accuracies = self.train_epoch(epoch, num_epochs)
            
            # Validate
            val_loss, val_task_losses, val_accuracies = self.validate_epoch(epoch)
            
            # Update history
            self.training_history['train_losses'].append(train_loss)
            self.training_history['val_losses'].append(val_loss)
            
            for task, loss in train_task_losses.items():
                self.training_history['task_losses'][f'train_{task}'].append(loss)
            
            for task, loss in val_task_losses.items():
                self.training_history['task_losses'][f'val_{task}'].append(loss)
            
            for task, acc in train_accuracies.items():
                self.training_history['accuracies'][f'train_{task}'].append(acc)
            
            for task, acc in val_accuracies.items():
                self.training_history['accuracies'][f'val_{task}'].append(acc)
            
            # Log epoch results
            logger.info(f"\nEpoch {epoch} Results:")
            logger.info(f"  Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            
            for task in ['food', 'harmful']:
                if task in train_accuracies and task in val_accuracies:
                    logger.info(f"  {task.title()} - Train Acc: {train_accuracies[task]:.3f}, "
                               f"Val Acc: {val_accuracies[task]:.3f}")
            
            # Save checkpoint
            is_best = val_loss < best_val_loss
            if is_best:
                best_val_loss = val_loss
            
            self.save_checkpoint(epoch, train_loss, val_loss, is_best)
        
        # Final results
        total_time = time.time() - start_time
        
        # Save final model
        self.model.save_lora_adapters(self.output_dir / "final_model")
        
        # Save training results
        results = {
            'training_completed': True,
            'total_epochs': num_epochs,
            'best_val_loss': float(best_val_loss),
            'total_time_hours': total_time / 3600,
            'food_categories': len(food_categories),
            'model_config': {
                'food_rank': food_rank,
                'harmful_rank': harmful_rank,
                'learning_rate': learning_rate,
                'batch_size': batch_size
            },
            'final_metrics': {
                'train_loss': self.training_history['train_losses'][-1],
                'val_loss': self.training_history['val_losses'][-1],
                'train_accuracies': {k: v[-1] for k, v in self.training_history['accuracies'].items() if 'train' in k},
                'val_accuracies': {k: v[-1] for k, v in self.training_history['accuracies'].items() if 'val' in k}
            },
            'training_history': self.training_history
        }
        
        with open(self.output_dir / "training_results.json", 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info("="*60)
        logger.info("MULTI-LORA TRAINING COMPLETED")
        logger.info(f"Best validation loss: {best_val_loss:.4f}")
        logger.info(f"Total time: {total_time/3600:.2f} hours")
        logger.info(f"Results saved to: {self.output_dir}")
        logger.info("="*60)
        
        return results
