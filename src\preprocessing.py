#!/usr/bin/env python3
"""
Data Preprocessing Module for VLM Model

This module contains all data preprocessing utilities including:
- Data loading and cleaning
- Sampling strategies
- Feature extraction
- Data transformation pipelines

Consolidated from: Preprocessing_Data/
"""

import pandas as pd
import numpy as np
import logging
import sys
from pathlib import Path
import time
from typing import Dict, List, Tuple, Optional, Union

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('preprocessing.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DataLoader:
    """Data loading utilities"""
    
    @staticmethod
    def load_csv(file_path: str, encoding: str = 'utf-8') -> pd.DataFrame:
        """Load CSV file with error handling"""
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            logger.info(f"Loaded {len(df):,} samples from {file_path}")
            return df
        except Exception as e:
            logger.error(f"Failed to load {file_path}: {e}")
            raise
    
    @staticmethod
    def load_multiple_csv(file_paths: List[str]) -> pd.DataFrame:
        """Load and concatenate multiple CSV files"""
        dfs = []
        for path in file_paths:
            df = DataLoader.load_csv(path)
            dfs.append(df)
        
        result = pd.concat(dfs, ignore_index=True)
        logger.info(f"Combined {len(file_paths)} files into {len(result):,} samples")
        return result


class DataCleaner:
    """Data cleaning utilities"""
    
    @staticmethod
    def clean_basic(df: pd.DataFrame, required_columns: List[str]) -> pd.DataFrame:
        """Basic data cleaning"""
        logger.info("Starting basic data cleaning...")
        original_count = len(df)
        
        # Remove rows with missing required columns
        df = df.dropna(subset=required_columns)
        
        # Clean URLs
        if 'URL' in df.columns:
            df = df[df['URL'].notna()]
            df = df[df['URL'].str.contains('http', case=False, na=False)]
        
        # Add normalized_name if missing
        if 'food_name' in df.columns and 'normalized_name' not in df.columns:
            df['normalized_name'] = df['food_name'].str.lower().str.strip()
        
        cleaned_count = len(df)
        logger.info(f"Cleaned: {original_count:,} → {cleaned_count:,} samples")
        return df
    
    @staticmethod
    def remove_duplicates(df: pd.DataFrame, subset: Optional[List[str]] = None) -> pd.DataFrame:
        """Remove duplicate rows"""
        original_count = len(df)
        df = df.drop_duplicates(subset=subset)
        cleaned_count = len(df)
        logger.info(f"Removed duplicates: {original_count:,} → {cleaned_count:,} samples")
        return df


class QualityScorer:
    """Quality scoring for data samples"""
    
    @staticmethod
    def calculate_quality_score(df: pd.DataFrame) -> pd.DataFrame:
        """Calculate quality scores for samples"""
        df = df.copy()
        df['quality_score'] = 0
        
        # URL quality (5 points)
        if 'URL' in df.columns:
            valid_urls = (df['URL'].str.len() > 30) & \
                        (df['URL'].str.contains('https', case=False, na=False))
            df.loc[valid_urls, 'quality_score'] += 5
        
        # Food name quality (3 points)
        if 'food_name' in df.columns:
            name_lengths = df['food_name'].str.len()
            good_names = (name_lengths >= 3) & (name_lengths <= 50)
            df.loc[good_names, 'quality_score'] += 3
        
        # Avoid generic terms (2 points)
        if 'food_name' in df.columns:
            generic_terms = ['món', 'thức ăn', 'đồ ăn', 'food', 'dish']
            non_generic = ~df['food_name'].str.lower().isin(generic_terms)
            df.loc[non_generic, 'quality_score'] += 2
        
        # Prefer samples with classification method (1 point)
        if 'classification_method' in df.columns:
            has_method = df['classification_method'].notna()
            df.loc[has_method, 'quality_score'] += 1
        
        return df


class DataSampler:
    """Data sampling utilities"""
    
    def __init__(self, target_samples: int = 32000):
        self.target_samples = target_samples
    
    def calculate_sampling_plan(self, df: pd.DataFrame, 
                              category_column: str = 'final_ultimate_category') -> Dict[str, int]:
        """Calculate balanced sampling plan"""
        categories = df[category_column].unique()
        category_counts = df[category_column].value_counts()
        
        total_categories = len(categories)
        base_per_category = self.target_samples // total_categories
        remainder = self.target_samples % total_categories
        
        logger.info(f"Base samples per category: {base_per_category}")
        logger.info(f"Extra samples to distribute: {remainder}")
        
        # Create sampling plan
        sampling_plan = {}
        
        # Give base amount to all categories
        for category in categories:
            available = category_counts[category]
            sampling_plan[category] = min(available, base_per_category)
        
        # Distribute remainder to largest categories
        if remainder > 0:
            largest_categories = category_counts.head(remainder).index
            for category in largest_categories:
                available = category_counts[category]
                current = sampling_plan[category]
                if current < available:
                    sampling_plan[category] += 1
        
        # Adjust if still short
        total_planned = sum(sampling_plan.values())
        while total_planned < self.target_samples:
            added = False
            for category in category_counts.index:
                if total_planned >= self.target_samples:
                    break
                available = category_counts[category]
                current = sampling_plan[category]
                if current < available:
                    sampling_plan[category] += 1
                    total_planned += 1
                    added = True
            
            if not added:  # No more samples available
                break
        
        final_total = sum(sampling_plan.values())
        logger.info(f"Final sampling plan: {final_total:,} samples")
        
        return sampling_plan
    
    def select_best_samples(self, samples_df: pd.DataFrame, count: int) -> List[Dict]:
        """Select best samples from a group"""
        if len(samples_df) <= count:
            return samples_df.to_dict('records')
        
        # Calculate quality scores
        samples_df = QualityScorer.calculate_quality_score(samples_df)
        
        # Sort by quality score and select top samples
        best_samples = samples_df.nlargest(count, 'quality_score')
        
        return best_samples.to_dict('records')
    
    def execute_sampling(self, df: pd.DataFrame, sampling_plan: Dict[str, int],
                        category_column: str = 'final_ultimate_category') -> pd.DataFrame:
        """Execute the sampling plan"""
        logger.info("Executing sampling...")
        start_time = time.time()
        
        all_samples = []
        processed = 0
        
        for category, target_count in sampling_plan.items():
            category_df = df[df[category_column] == category]
            
            # Get unique food names in this category
            unique_foods = category_df['food_name'].unique()
            
            if len(unique_foods) >= target_count:
                # Enough unique foods: select one sample per food
                selected_foods = np.random.choice(unique_foods, target_count, replace=False)
                for food in selected_foods:
                    food_samples = category_df[category_df['food_name'] == food]
                    best_samples = self.select_best_samples(food_samples, 1)
                    all_samples.extend(best_samples)
            else:
                # Not enough unique foods: select multiple samples per food
                samples_per_food = target_count // len(unique_foods)
                remainder = target_count % len(unique_foods)
                
                for i, food in enumerate(unique_foods):
                    food_samples = category_df[category_df['food_name'] == food]
                    count = samples_per_food + (1 if i < remainder else 0)
                    best_samples = self.select_best_samples(food_samples, count)
                    all_samples.extend(best_samples)
            
            processed += 1
            if processed % 200 == 0:
                progress = (processed / len(sampling_plan)) * 100
                logger.info(f"Progress: {progress:.1f}% ({processed}/{len(sampling_plan)})")
        
        # Create final dataframe
        result_df = pd.DataFrame(all_samples)
        
        elapsed = time.time() - start_time
        logger.info(f"Sampling completed in {elapsed/60:.1f} minutes")
        logger.info(f"Final result: {len(result_df):,} samples")
        
        return result_df


class PreprocessingPipeline:
    """Main preprocessing pipeline"""
    
    def __init__(self, target_samples: int = 32000):
        self.target_samples = target_samples
        self.sampler = DataSampler(target_samples)
    
    def run_full_pipeline(self, input_file: str, output_file: str,
                         required_columns: List[str] = None) -> pd.DataFrame:
        """Run the complete preprocessing pipeline"""
        if required_columns is None:
            required_columns = ['food_name', 'final_ultimate_category']
        
        logger.info("Starting full preprocessing pipeline")
        
        try:
            # Load data
            df = DataLoader.load_csv(input_file)
            
            # Clean data
            df = DataCleaner.clean_basic(df, required_columns)
            df = DataCleaner.remove_duplicates(df)
            
            # Calculate sampling plan
            sampling_plan = self.sampler.calculate_sampling_plan(df)
            
            # Execute sampling
            result_df = self.sampler.execute_sampling(df, sampling_plan)
            
            # Save results
            self.save_results(result_df, output_file)
            
            logger.info("Preprocessing pipeline completed successfully!")
            return result_df
            
        except Exception as e:
            logger.error(f"Preprocessing pipeline failed: {e}")
            raise
    
    def save_results(self, df: pd.DataFrame, output_file: str):
        """Save preprocessing results"""
        logger.info("Saving preprocessing results...")
        
        # Save dataset
        df.to_csv(output_file, index=False, encoding='utf-8')
        logger.info(f"Dataset saved: {output_file}")
        
        # Generate report
        report_file = output_file.replace('.csv', '_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("PREPROCESSING REPORT\n")
            f.write("="*50 + "\n\n")
            f.write(f"Target samples: {self.target_samples:,}\n")
            f.write(f"Actual samples: {len(df):,}\n")
            f.write(f"Achievement rate: {len(df)/self.target_samples*100:.1f}%\n")
            f.write(f"Total categories: {df['final_ultimate_category'].nunique():,}\n")
            f.write(f"Unique food names: {df['food_name'].nunique():,}\n")
            
            if 'URL' in df.columns:
                f.write(f"Valid URLs: {df['URL'].notna().sum():,}\n")
        
        logger.info(f"Report saved: {report_file}")


# Convenience functions for backward compatibility
def load_and_clean_data(input_file: str, required_columns: List[str] = None) -> pd.DataFrame:
    """Load and clean data (convenience function)"""
    if required_columns is None:
        required_columns = ['food_name', 'final_ultimate_category']
    
    df = DataLoader.load_csv(input_file)
    df = DataCleaner.clean_basic(df, required_columns)
    df = DataCleaner.remove_duplicates(df)
    return df


def create_sample_dataset(input_file: str, output_file: str, 
                         target_samples: int = 32000) -> pd.DataFrame:
    """Create sample dataset (convenience function)"""
    pipeline = PreprocessingPipeline(target_samples)
    return pipeline.run_full_pipeline(input_file, output_file)


if __name__ == "__main__":
    # Example usage
    pipeline = PreprocessingPipeline(target_samples=32000)
    result = pipeline.run_full_pipeline(
        input_file="data/raw_dataset.csv",
        output_file="data/processed_dataset.csv"
    )
