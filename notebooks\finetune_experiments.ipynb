{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# VLM Finetuning Experiments\n", "\n", "This notebook contains experiments for finetuning VLM models using different methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary modules\n", "import sys\n", "sys.path.append('..')\n", "\n", "from src import preprocessing\n", "from src import finetune_multi_lora\n", "from src import finetune_progressive"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add your preprocessing experiments here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multi-LoRA Finetuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add your Multi-LoRA experiments here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Progressive Method Finetuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add your Progressive Method experiments here"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}