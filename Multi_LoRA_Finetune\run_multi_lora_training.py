#!/usr/bin/env python3
"""
Main script để chạy Multi-LoRA Training cho Food + Harmful Detection
- Optimized hyperparameters dựa trên Progressive training lessons
- Comprehensive logging và monitoring
- Auto model selection và evaluation
"""

import argparse
import logging
import torch
import json
from pathlib import Path
import sys

from multi_lora_trainer import <PERSON>Lo<PERSON><PERSON>rain<PERSON>
from multi_task_dataset_pipeline import analyze_multi_task_dataset

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Multi-LoRA Training for Food + Harmful Detection')
    
    # Data paths
    parser.add_argument('--food_data', type=str, 
                       default='../Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_DATASET.csv',
                       help='Path to food dataset')
    parser.add_argument('--harmful_data', type=str,
                       default='harmful_dataset.csv',
                       help='Path to harmful dataset')
    
    # Model configuration
    parser.add_argument('--model_path', type=str, default='Vintern-1B-v3_5',
                       help='Base model path')
    parser.add_argument('--food_rank', type=int, default=64,
                       help='LoRA rank for food classification (32, 64, 128)')
    parser.add_argument('--harmful_rank', type=int, default=32,
                       help='LoRA rank for harmful detection (16, 32, 64)')
    
    # Training configuration
    parser.add_argument('--epochs', type=int, default=12,
                       help='Number of training epochs')
    parser.add_argument('--learning_rate', type=float, default=2e-4,
                       help='Learning rate')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size')
    
    # Output
    parser.add_argument('--output_dir', type=str, default='multi_lora_results',
                       help='Output directory')
    parser.add_argument('--experiment_name', type=str, default='',
                       help='Experiment name suffix')
    
    # Advanced options
    parser.add_argument('--analyze_only', action='store_true',
                       help='Only analyze datasets without training')
    parser.add_argument('--resume', type=str, default='',
                       help='Resume from checkpoint')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    
    return parser.parse_args()

def validate_arguments(args):
    """Validate command line arguments"""
    errors = []
    
    # Check data paths
    if not Path(args.food_data).exists():
        errors.append(f"Food dataset not found: {args.food_data}")
    
    # Validate ranks
    valid_ranks = [8, 16, 32, 64, 128, 256]
    if args.food_rank not in valid_ranks:
        errors.append(f"Invalid food_rank: {args.food_rank}. Must be one of {valid_ranks}")
    
    if args.harmful_rank not in valid_ranks:
        errors.append(f"Invalid harmful_rank: {args.harmful_rank}. Must be one of {valid_ranks}")
    
    # Validate training params
    if args.epochs < 1:
        errors.append("Epochs must be >= 1")
    
    if args.learning_rate <= 0:
        errors.append("Learning rate must be > 0")
    
    if args.batch_size < 1:
        errors.append("Batch size must be >= 1")
    
    # Check device
    if args.device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA not available, switching to CPU")
        args.device = 'cpu'
    
    if errors:
        for error in errors:
            logger.error(error)
        sys.exit(1)
    
    return args

def create_experiment_config(args):
    """Create experiment configuration"""
    config = {
        'experiment_name': f"multi_lora_food{args.food_rank}_harmful{args.harmful_rank}_{args.experiment_name}",
        'model_config': {
            'model_path': args.model_path,
            'food_rank': args.food_rank,
            'harmful_rank': args.harmful_rank
        },
        'training_config': {
            'epochs': args.epochs,
            'learning_rate': args.learning_rate,
            'batch_size': args.batch_size,
            'device': args.device
        },
        'data_config': {
            'food_data_path': args.food_data,
            'harmful_data_path': args.harmful_data
        },
        'output_config': {
            'output_dir': args.output_dir,
            'resume_from': args.resume
        }
    }
    
    return config

def analyze_datasets(args):
    """Analyze datasets before training"""
    logger.info("="*60)
    logger.info("DATASET ANALYSIS")
    logger.info("="*60)
    
    try:
        analysis = analyze_multi_task_dataset(args.food_data, args.harmful_data)
        
        logger.info("Dataset Analysis Results:")
        logger.info(f"  Total samples: {analysis['total_samples']:,}")
        logger.info(f"  Food samples: {analysis['task_distribution']['food_classification']:,}")
        logger.info(f"  Harmful samples: {analysis['task_distribution']['harmful_detection']:,}")
        logger.info(f"  Food categories: {analysis['food_categories']}")
        logger.info(f"  Balance ratio: {analysis['balance_ratio']:.1%}")
        
        # Top food categories
        logger.info("\nTop 10 Food Categories:")
        for i, (category, count) in enumerate(analysis['food_top_categories'].items(), 1):
            logger.info(f"  {i:2d}. {category}: {count:,} samples")
        
        # Harmful distribution
        logger.info("\nHarmful Content Distribution:")
        for label, count in analysis['harmful_distribution'].items():
            logger.info(f"  {label}: {count:,} samples")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Dataset analysis failed: {e}")
        return None

def estimate_training_time(config, analysis):
    """Estimate training time dựa trên configuration"""
    if not analysis:
        return "Unknown"
    
    # Base estimates (rough)
    samples_per_epoch = analysis['total_samples']
    batch_size = config['training_config']['batch_size']
    epochs = config['training_config']['epochs']
    
    batches_per_epoch = samples_per_epoch // batch_size
    total_batches = batches_per_epoch * epochs
    
    # Estimate time per batch (depends on ranks and device)
    food_rank = config['model_config']['food_rank']
    harmful_rank = config['model_config']['harmful_rank']
    device = config['training_config']['device']
    
    # Time estimates (seconds per batch)
    if device == 'cuda':
        base_time = 2.0  # Base time for CUDA
        rank_factor = (food_rank + harmful_rank) / 96  # Normalized by typical ranks
        time_per_batch = base_time * rank_factor
    else:
        time_per_batch = 8.0  # CPU is much slower
    
    total_time_hours = (total_batches * time_per_batch) / 3600
    
    return f"{total_time_hours:.1f} hours"

def main():
    """Main function"""
    logger.info("="*60)
    logger.info("MULTI-LORA TRAINING FOR FOOD + HARMFUL DETECTION")
    logger.info("="*60)
    
    # Parse and validate arguments
    args = parse_arguments()
    args = validate_arguments(args)
    
    # Create experiment config
    config = create_experiment_config(args)
    
    logger.info("Experiment Configuration:")
    logger.info(f"  Name: {config['experiment_name']}")
    logger.info(f"  Food LoRA rank: {config['model_config']['food_rank']}")
    logger.info(f"  Harmful LoRA rank: {config['model_config']['harmful_rank']}")
    logger.info(f"  Epochs: {config['training_config']['epochs']}")
    logger.info(f"  Learning rate: {config['training_config']['learning_rate']}")
    logger.info(f"  Batch size: {config['training_config']['batch_size']}")
    logger.info(f"  Device: {config['training_config']['device']}")
    
    # Analyze datasets
    analysis = analyze_datasets(args)
    
    if args.analyze_only:
        logger.info("Analysis complete. Exiting (--analyze_only flag set).")
        return
    
    # Estimate training time
    estimated_time = estimate_training_time(config, analysis)
    logger.info(f"\nEstimated training time: {estimated_time}")
    
    # Confirm before starting
    if analysis and analysis['total_samples'] > 100000:
        response = input(f"\nDataset is large ({analysis['total_samples']:,} samples). Continue? [y/N]: ")
        if response.lower() != 'y':
            logger.info("Training cancelled by user.")
            return
    
    # Create output directory
    output_dir = Path(args.output_dir) / config['experiment_name']
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save experiment config
    with open(output_dir / "experiment_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    # Initialize trainer
    trainer = MultiLoRATrainer(
        model_path=args.model_path,
        output_dir=str(output_dir),
        device=args.device
    )
    
    # Start training
    try:
        results = trainer.train(
            food_data_path=args.food_data,
            harmful_data_path=args.harmful_data,
            num_epochs=args.epochs,
            learning_rate=args.learning_rate,
            batch_size=args.batch_size,
            food_rank=args.food_rank,
            harmful_rank=args.harmful_rank
        )
        
        logger.info("="*60)
        logger.info("TRAINING COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        logger.info(f"Best validation loss: {results['best_val_loss']:.4f}")
        logger.info(f"Total training time: {results['total_time_hours']:.2f} hours")
        logger.info(f"Results saved to: {output_dir}")
        
        # Final metrics
        final_metrics = results['final_metrics']
        logger.info("\nFinal Metrics:")
        for metric, value in final_metrics['val_accuracies'].items():
            logger.info(f"  {metric}: {value:.3f}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
