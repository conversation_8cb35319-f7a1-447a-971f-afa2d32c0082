# Complete Git Workflow - VLM Parser Reorganization

## 🎯 Mục tiêu
1. Gỡ bỏ hoàn toàn code cũ trên Git
2. Push code đã tổ chức lại lên Git
3. Thêm training folders sau

---

## 📋 PHASE 1: Chuẩn bị và Clean Repository

### Bước 1: Backup code hiện tại
```bash
# Tạo backup trước khi clean
cp -r . ../VLM_Parser_backup_manual
echo "✅ Code backed up to ../VLM_Parser_backup_manual"
```

### Bước 2: Chạy script clean (Recommended)
```bash
# Chạy script tự động
python clean_and_reorganize_git.py
```

### Bước 3: Clean manual (Alternative)
```bash
# Nếu không dùng script, clean manual:

# Xóa tất cả file (giữ .git)
find . -maxdepth 1 ! -name '.git' ! -name '.' ! -name '..' -exec rm -rf {} +

# Hoặc trên Windows:
# for /f "delims=" %i in ('dir /b /a-d') do if not "%i"==".git" del "%i"
# for /f "delims=" %i in ('dir /b /ad') do if not "%i"==".git" rmdir /s /q "%i"
```

---

## 📋 PHASE 2: Tạo Structure Mới

### Bước 1: Tạo thư mục organized
```bash
mkdir -p src models data tests docs notebooks scripts configs
echo "✅ Directory structure created"
```

### Bước 2: Tạo file cấu hình chính

#### .gitignore
```bash
cat > .gitignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.bin

# Large data files (will use Git LFS)
*.csv
*.json
*.parquet
*.h5
*.hdf5
*.pkl
*.pickle

# Model files (will use Git LFS)
*.safetensors
*.model

# Logs
*.log
logs/
wandb/

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
EOF
```

#### .gitattributes (Git LFS)
```bash
cat > .gitattributes << 'EOF'
# Git LFS tracking
*.safetensors filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text

# Large data files
*.csv filter=lfs diff=lfs merge=lfs -text
*.json filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
EOF
```

#### requirements.txt
```bash
cat > requirements.txt << 'EOF'
# Core ML libraries
torch>=2.0.0
transformers>=4.30.0
peft>=0.4.0
accelerate>=0.20.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.6.0
seaborn>=0.12.0

# Progress bars and logging
tqdm>=4.65.0

# Web UI
fastapi>=0.100.0
uvicorn>=0.22.0
streamlit>=1.25.0

# Image processing
Pillow>=9.5.0

# Text processing
nltk>=3.8.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0
requests>=2.31.0

# Development
pytest>=7.4.0
black>=23.0.0

# Jupyter
jupyter>=1.0.0
EOF
```

### Bước 3: Copy code organized từ backup
```bash
# Copy các file đã tổ chức (nếu có)
cp ../VLM_Parser_backup_manual/src/* src/ 2>/dev/null || echo "No src files to copy"
cp ../VLM_Parser_backup_manual/main.py . 2>/dev/null || echo "No main.py to copy"
cp ../VLM_Parser_backup_manual/README_NEW.md README.md 2>/dev/null || echo "No README to copy"
```

---

## 📋 PHASE 3: Git Operations

### Bước 1: Cài đặt Git LFS
```bash
# Cài đặt Git LFS
git lfs install

# Verify
git lfs version
```

### Bước 2: Add và Commit code mới
```bash
# Kiểm tra trạng thái
git status

# Add tất cả file
git add .

# Commit với message chi tiết
git commit -m "Complete project reorganization and cleanup

🧹 Repository Cleanup:
- Removed all legacy code and files
- Clean slate for organized structure

🏗️ New Structure:
- src/ - Organized source modules
- models/ - Model storage (Git LFS ready)
- data/ - Dataset storage (Git LFS ready)
- tests/ - Unit testing framework
- docs/ - Documentation
- notebooks/ - Jupyter experiments
- scripts/ - Utility scripts
- configs/ - Configuration files

⚙️ Configuration:
- .gitignore - Proper file exclusions
- .gitattributes - Git LFS for large files
- requirements.txt - Python dependencies
- LICENSE - MIT license
- README.md - Comprehensive documentation

🎯 Features Ready:
- Multi-LoRA fine-tuning framework
- Progressive training methodology
- Data preprocessing pipeline
- Model testing utilities
- Web UI interface
- Professional project structure

📦 Git LFS Support:
- Model files (*.pth, *.safetensors, *.bin)
- Large datasets (*.csv, *.json)
- Ready for large file management

🚀 Ready for collaborative development!"
```

### Bước 3: Push lên remote
```bash
# Push lên master branch
git push origin master

# Hoặc nếu cần force push (cẩn thận!)
git push -f origin master
```

### Bước 4: Tạo release tag
```bash
# Tạo tag cho version đầu tiên
git tag -a v1.0.0 -m "v1.0.0 - Clean organized release

- Complete project reorganization
- Professional structure
- Git LFS support
- Ready for development"

# Push tag
git push origin v1.0.0
```

---

## 📋 PHASE 4: Thêm Training Folders (Sau)

### Khi nào thêm training folders:
```bash
# Copy training folders từ backup
cp -r ../VLM_Parser_backup_manual/Multi_LoRA_Finetune .
cp -r ../VLM_Parser_backup_manual/Progressive_Method_Finetune .
cp -r ../VLM_Parser_backup_manual/Method_For_Test_With_Model_VinternV3.5 .
cp -r ../VLM_Parser_backup_manual/UI_For_VLM .
cp -r ../VLM_Parser_backup_manual/Preprocessing_Data .

# Add to git
git add Multi_LoRA_Finetune/
git add Progressive_Method_Finetune/
git add Method_For_Test_With_Model_VinternV3.5/
git add UI_For_VLM/
git add Preprocessing_Data/

# Commit
git commit -m "Add original training implementations

- Multi_LoRA_Finetune/ - Multi-LoRA training code
- Progressive_Method_Finetune/ - Progressive training methods
- Method_For_Test_With_Model_VinternV3.5/ - Model testing
- UI_For_VLM/ - Web interface
- Preprocessing_Data/ - Data preprocessing

These folders contain the original implementations
for backward compatibility and reference."

# Push
git push origin master
```

---

## 📋 PHASE 5: Verification

### Kiểm tra kết quả:
```bash
# Xem commit history
git log --oneline -5

# Xem file structure
git ls-files

# Kiểm tra Git LFS
git lfs ls-files

# Xem repository size
git count-objects -vH

# Kiểm tra remote
git remote -v
```

---

## 🎉 Kết quả mong đợi

Sau khi hoàn thành, repository sẽ có:

✅ **Clean structure**: Organized và professional
✅ **Git LFS ready**: Sẵn sàng cho file lớn
✅ **Documentation**: README và docs đầy đủ
✅ **Development ready**: Tests, configs, scripts
✅ **Backward compatible**: Training folders (thêm sau)

**Repository size**: Nhỏ gọn, chỉ chứa code và docs
**Large files**: Sẽ dùng Git LFS khi cần
**Collaboration**: Sẵn sàng cho team development

---

## 🚨 Lưu ý quan trọng

1. **Backup**: Luôn backup trước khi clean
2. **Force push**: Chỉ dùng khi chắc chắn
3. **Git LFS**: Cài đặt trước khi push file lớn
4. **Team**: Thông báo team trước khi reorganize

**Ready to execute! 🚀**
