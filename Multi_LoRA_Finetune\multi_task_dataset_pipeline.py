#!/usr/bin/env python3
"""
Multi-Task Dataset Pipeline cho Food + Harmful Detection
- Balanced sampling giữa 2 tasks
- Unified data format
- Task-aware augmentation
- Quality control và validation
"""

import torch
import pandas as pd
import numpy as np
import json
import logging
import random
from pathlib import Path
from PIL import Image
import requests
from io import BytesIO
import torchvision.transforms as transforms
from torch.utils.data import Dataset, DataLoader
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiTaskDataset(Dataset):
    """Multi-task dataset cho Food + Harmful detection"""
    
    def __init__(self, food_data_path, harmful_data_path, tokenizer, 
                 task_balance_ratio=0.5, is_training=True, max_samples_per_task=None):
        """
        Args:
            food_data_path: Path to food dataset (FINAL_ULTIMATE_COMPLETE_DATASET.csv)
            harmful_data_path: Path to harmful dataset 
            task_balance_ratio: Tỷ lệ food vs harmful (0.5 = 50/50)
            max_samples_per_task: Giới hạn samples per task (None = unlimited)
        """
        self.tokenizer = tokenizer
        self.task_balance_ratio = task_balance_ratio
        self.is_training = is_training
        
        # Load datasets
        self.food_data = self._load_food_dataset(food_data_path, max_samples_per_task)
        self.harmful_data = self._load_harmful_dataset(harmful_data_path, max_samples_per_task)
        
        # Create unified dataset
        self.unified_data = self._create_unified_dataset()
        
        # Task-aware transforms
        self.food_transform = self._get_food_transforms()
        self.harmful_transform = self._get_harmful_transforms()
        
        logger.info(f"Multi-task dataset created:")
        logger.info(f"  Food samples: {len(self.food_data)}")
        logger.info(f"  Harmful samples: {len(self.harmful_data)}")
        logger.info(f"  Total samples: {len(self.unified_data)}")
        logger.info(f"  Task balance: {task_balance_ratio:.1%} food, {1-task_balance_ratio:.1%} harmful")
    
    def _load_food_dataset(self, data_path, max_samples=None):
        """Load Vietnamese food dataset"""
        logger.info(f"Loading food dataset from {data_path}")
        
        if str(data_path).endswith('.csv'):
            df = pd.read_csv(data_path)
        else:
            df = pd.read_json(data_path)
        
        # Filter classified items only
        df = df[df['final_ultimate_category'] != 'Chưa phân loại'].copy()
        
        # Sample if needed
        if max_samples and len(df) > max_samples:
            df = df.sample(n=max_samples, random_state=42)
        
        # Convert to unified format
        food_data = []
        for _, row in df.iterrows():
            food_data.append({
                'image_url': row['URL'],
                'task_type': 'food_classification',
                'task_id': 0,
                'label': row['final_ultimate_category'],
                'instruction': self._get_food_instruction(row['final_ultimate_category']),
                'response': self._get_food_response(row['food_name'], row['final_ultimate_category']),
                'metadata': {
                    'food_name': row['food_name'],
                    'normalized_name': row.get('normalized_name', ''),
                    'classification_method': row.get('classification_method', '')
                }
            })
        
        return food_data
    
    def _load_harmful_dataset(self, data_path, max_samples=None):
        """Load harmful content dataset"""
        logger.info(f"Loading harmful dataset from {data_path}")
        
        # Tạo synthetic harmful dataset nếu chưa có
        if not Path(data_path).exists():
            logger.warning(f"Harmful dataset not found at {data_path}, creating synthetic dataset")
            return self._create_synthetic_harmful_dataset(max_samples or 10000)
        
        # Load real harmful dataset
        if str(data_path).endswith('.csv'):
            df = pd.read_csv(data_path)
        else:
            df = pd.read_json(data_path)
        
        # Sample if needed
        if max_samples and len(df) > max_samples:
            df = df.sample(n=max_samples, random_state=42)
        
        # Convert to unified format
        harmful_data = []
        for _, row in df.iterrows():
            harmful_data.append({
                'image_url': row['image_url'],
                'task_type': 'harmful_detection',
                'task_id': 1,
                'label': row['label'],  # 'safe' or 'harmful'
                'instruction': self._get_harmful_instruction(),
                'response': self._get_harmful_response(row['label']),
                'metadata': {
                    'harmful_type': row.get('harmful_type', 'unknown'),
                    'confidence': row.get('confidence', 1.0)
                }
            })
        
        return harmful_data
    
    def _create_synthetic_harmful_dataset(self, num_samples):
        """Tạo synthetic harmful dataset cho testing"""
        logger.info(f"Creating synthetic harmful dataset with {num_samples} samples")
        
        # Synthetic harmful categories
        harmful_types = ['nudity', 'violence', 'drugs', 'weapons', 'hate_speech']
        
        synthetic_data = []
        for i in range(num_samples):
            # 70% safe, 30% harmful để cân bằng
            is_harmful = random.random() < 0.3
            label = 'harmful' if is_harmful else 'safe'
            harmful_type = random.choice(harmful_types) if is_harmful else 'safe'
            
            synthetic_data.append({
                'image_url': f"https://example.com/synthetic_image_{i}.jpg",
                'task_type': 'harmful_detection',
                'task_id': 1,
                'label': label,
                'instruction': self._get_harmful_instruction(),
                'response': self._get_harmful_response(label),
                'metadata': {
                    'harmful_type': harmful_type,
                    'confidence': random.uniform(0.8, 1.0),
                    'synthetic': True
                }
            })
        
        return synthetic_data
    
    def _create_unified_dataset(self):
        """Tạo unified dataset với balanced sampling"""
        total_food = len(self.food_data)
        total_harmful = len(self.harmful_data)
        
        # Calculate target samples per task
        if total_food == 0 or total_harmful == 0:
            # Fallback: use all available data
            unified = self.food_data + self.harmful_data
        else:
            # Balanced sampling
            target_food = int(min(total_food, total_harmful) * self.task_balance_ratio / (1 - self.task_balance_ratio))
            target_harmful = int(min(total_food, total_harmful))
            
            # Ensure we don't exceed available data
            target_food = min(target_food, total_food)
            target_harmful = min(target_harmful, total_harmful)
            
            # Sample data
            sampled_food = random.sample(self.food_data, target_food)
            sampled_harmful = random.sample(self.harmful_data, target_harmful)
            
            unified = sampled_food + sampled_harmful
        
        # Shuffle
        random.shuffle(unified)
        
        return unified
    
    def _get_food_transforms(self):
        """Food-specific image transforms"""
        if self.is_training:
            return transforms.Compose([
                transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
                transforms.RandomHorizontalFlip(p=0.3),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            return transforms.Compose([
                transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
    
    def _get_harmful_transforms(self):
        """Harmful detection specific transforms"""
        if self.is_training:
            return transforms.Compose([
                transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
                transforms.RandomHorizontalFlip(p=0.2),  # Less aggressive for harmful detection
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            return transforms.Compose([
                transforms.Resize((448, 448), interpolation=transforms.InterpolationMode.BICUBIC),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
    
    def _get_food_instruction(self, category):
        """Generate food classification instruction"""
        templates = [
            "What Vietnamese food is shown in this image?",
            "Identify this Vietnamese dish and its category.",
            "What type of Vietnamese cuisine is displayed here?",
            f"Is this image showing {category}? Describe the Vietnamese food."
        ]
        return random.choice(templates)
    
    def _get_food_response(self, food_name, category):
        """Generate food classification response"""
        return f"This is {food_name}, which belongs to the {category} category of Vietnamese cuisine."
    
    def _get_harmful_instruction(self):
        """Generate harmful detection instruction"""
        templates = [
            "Is this image safe for work and appropriate for all audiences?",
            "Analyze this image for any harmful or inappropriate content.",
            "Does this image contain any NSFW or harmful material?",
            "Classify this image as safe or potentially harmful."
        ]
        return random.choice(templates)
    
    def _get_harmful_response(self, label):
        """Generate harmful detection response"""
        if label == 'safe':
            return "This image is safe and appropriate for all audiences."
        else:
            return "This image contains potentially harmful or inappropriate content."
    
    def _load_image(self, image_url, task_type):
        """Load image với task-specific transforms"""
        try:
            # Load image
            if image_url.startswith('http'):
                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.get(image_url, headers=headers, timeout=10, verify=False)
                response.raise_for_status()
                image = Image.open(BytesIO(response.content))
            else:
                image = Image.open(image_url)
            
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Apply task-specific transforms
            if task_type == 'food_classification':
                tensor = self.food_transform(image)
            else:
                tensor = self.harmful_transform(image)
            
            return tensor.unsqueeze(0)
            
        except Exception as e:
            logger.warning(f"Failed to load image {image_url}: {e}")
            # Return zero tensor
            return torch.zeros(1, 3, 448, 448)
    
    def __len__(self):
        return len(self.unified_data)
    
    def __getitem__(self, idx):
        item = self.unified_data[idx]
        
        # Load image
        image_tensor = self._load_image(item['image_url'], item['task_type'])
        
        return {
            'image_tensor': image_tensor,
            'task_type': item['task_type'],
            'task_id': item['task_id'],
            'label': item['label'],
            'instruction': item['instruction'],
            'response': item['response'],
            'metadata': item['metadata']
        }

def create_multi_task_dataloaders(food_data_path, harmful_data_path, tokenizer,
                                 batch_size=8, task_balance_ratio=0.5, 
                                 train_split=0.8, val_split=0.1):
    """Create train/val/test dataloaders cho multi-task"""
    
    # Create full dataset
    full_dataset = MultiTaskDataset(
        food_data_path, harmful_data_path, tokenizer,
        task_balance_ratio=task_balance_ratio, is_training=True
    )
    
    # Split dataset
    total_size = len(full_dataset)
    train_size = int(total_size * train_split)
    val_size = int(total_size * val_split)
    test_size = total_size - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        num_workers=4, pin_memory=True, collate_fn=multi_task_collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=2, pin_memory=True, collate_fn=multi_task_collate_fn
    )
    
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=2, pin_memory=True, collate_fn=multi_task_collate_fn
    )
    
    return train_loader, val_loader, test_loader

def multi_task_collate_fn(batch):
    """Collate function cho multi-task batch"""
    images = torch.cat([item['image_tensor'] for item in batch], dim=0)
    task_types = [item['task_type'] for item in batch]
    task_ids = torch.tensor([item['task_id'] for item in batch])
    labels = [item['label'] for item in batch]
    instructions = [item['instruction'] for item in batch]
    responses = [item['response'] for item in batch]
    metadata = [item['metadata'] for item in batch]
    
    return {
        'images': images,
        'task_types': task_types,
        'task_ids': task_ids,
        'labels': labels,
        'instructions': instructions,
        'responses': responses,
        'metadata': metadata
    }

def analyze_multi_task_dataset(food_data_path, harmful_data_path):
    """Analyze multi-task dataset distribution"""
    logger.info("Analyzing multi-task dataset...")
    
    # Create dataset for analysis
    dataset = MultiTaskDataset(food_data_path, harmful_data_path, None)
    
    # Task distribution
    task_counts = Counter([item['task_type'] for item in dataset.unified_data])
    
    # Label distribution per task
    food_labels = [item['label'] for item in dataset.unified_data if item['task_type'] == 'food_classification']
    harmful_labels = [item['label'] for item in dataset.unified_data if item['task_type'] == 'harmful_detection']
    
    food_label_counts = Counter(food_labels)
    harmful_label_counts = Counter(harmful_labels)
    
    analysis = {
        'total_samples': len(dataset),
        'task_distribution': dict(task_counts),
        'food_categories': len(food_label_counts),
        'food_top_categories': dict(food_label_counts.most_common(10)),
        'harmful_distribution': dict(harmful_label_counts),
        'balance_ratio': task_counts['food_classification'] / len(dataset)
    }
    
    logger.info("Dataset Analysis:")
    logger.info(f"  Total samples: {analysis['total_samples']}")
    logger.info(f"  Food samples: {task_counts['food_classification']}")
    logger.info(f"  Harmful samples: {task_counts['harmful_detection']}")
    logger.info(f"  Food categories: {analysis['food_categories']}")
    logger.info(f"  Balance ratio: {analysis['balance_ratio']:.2%}")
    
    return analysis
