#!/usr/bin/env python3
"""
Script to organize VLM project structure for better Git management

This script will:
1. Keep original folders as-is (for reference)
2. Create organized src/ structure with consolidated modules
3. Set up proper .gitignore for large files
4. Create requirements.txt and main.py
"""

import os
import shutil
from pathlib import Path

def organize_project():
    """Organize project structure"""
    print("🚀 Organizing VLM project structure...")
    
    # Current directory
    root = Path(".")
    
    # Create organized structure (already done)
    print("✅ Organized structure already created:")
    print("   - src/ (consolidated modules)")
    print("   - models/ (for trained models)")
    print("   - data/ (for datasets)")
    print("   - tests/ (unit tests)")
    print("   - docs/ (documentation)")
    print("   - notebooks/ (experiments)")
    
    # Check what we have
    print("\n📁 Current structure:")
    for item in sorted(root.iterdir()):
        if item.is_dir() and not item.name.startswith('.'):
            print(f"   📂 {item.name}/")
        elif item.is_file() and item.suffix in ['.py', '.md', '.txt']:
            print(f"   📄 {item.name}")
    
    print("\n🎯 Project is now organized with:")
    print("   ✅ Clean src/ modules")
    print("   ✅ Proper .gitignore")
    print("   ✅ requirements.txt")
    print("   ✅ main.py entry point")
    print("   ✅ Updated README")
    print("   ✅ Original folders preserved")
    
    print("\n📋 Next steps:")
    print("   1. Review the organized structure")
    print("   2. Test the main.py entry point")
    print("   3. Run: git add .")
    print("   4. Run: git commit -m 'Organize project structure'")
    print("   5. Run: git push origin master")
    
    print("\n🚨 Note: Large data files are ignored by .gitignore")
    print("   Only code and documentation will be pushed to Git")

if __name__ == "__main__":
    organize_project()
