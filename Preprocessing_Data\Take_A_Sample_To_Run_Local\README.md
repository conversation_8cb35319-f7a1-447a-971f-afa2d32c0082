## README — Lấy mẫu để huấn luyện local (25 mẫu/category và 32k tổng)

Mục tiêu: T<PERSON><PERSON> các tập dữ liệu đại diện, cân bằng cho fine-tune cục bộ: (a) Balanced 25 mẫu/category; (b) Tập 32,000 mẫu phủ rộng nhiều category.

Nguồn: FINAL_ULTIMATE_COMPLETE_DATASET.csv (đã hợp nhất sau tiền xử lý).

---

## 1) Balanced 25 mẫu/category
Script: All_Data_After_Preprocessing/Analyze_Prepare_For_Train/analyze_and_prepare_training_data.py

Thuật toán (create_balanced_sample):
- Lọc ra các item đã phân loại (final_ultimate_category != "Chưa phân loại") và URL hợp lệ
- Với mỗi category:
  - Nếu số mẫu hợp lệ >= 25 → lấy ngẫu nhiên đúng 25 mẫu
  - Nếu 10 ≤ số mẫu < 25 → lấy toàn bộ (chấp nhận thiếu) và ghi log cảnh báo
  - Nếu < 10 → bỏ qua category (ngưỡng chất lượng)
- Hợp nhất thành balanced_df rồi ghi ra bộ dữ liệu huấn luyện chuẩn VLM

Đầu ra (prepare_training_format):
- vintern_training_data/
  - training_data.json: [{image_url, food_name, category, normalized_name, is_combo, classification_method}, ...]
  - balanced_training_dataset.csv: Bản CSV cân bằng
  - category_mapping.json: Ánh xạ category → id
  - training_statistics.json: Số liệu tổng hợp (min/max/avg per category)

Chạy nhanh:
- cd Preprocessing_Data/All_Data_After_Preprocessing/Analyze_Prepare_For_Train
- python analyze_and_prepare_training_data.py  (mặc định 25 mẫu/category)

Lưu ý:
- Script có bước validate URL mẫu (HEAD request) để ước tính tỉ lệ URL hợp lệ
- Có báo cáo tổng hợp: VLM_TRAINING_PREPARATION_REPORT.md

---

## 2) Lấy đúng 32,000 mẫu phủ đều (Final32kSampler)
Script: Take_A_Sample_To_Run_Local/Final_32k_Sampler.py

Thiết kế:
- input_file: Final_Ultimate_Complete_Dataset.csv (mặc định)
- target_samples: 32,000
- Tính toán kế hoạch sampling theo category:
  - base_per_category = floor(32000 / num_categories)
  - remainder = 32000 % num_categories → phân bổ thêm cho các category lớn nhất
  - Với từng category: sampling_plan[cat] = min(available, base_per_category) + (phần dư nếu còn)
- Thực thi sampling theo kế hoạch, ghi báo cáo chi tiết (tỉ lệ phủ, thiếu, size tệp)

Đầu ra:
- Final_32k_Dataset.csv
- Final_32k_Dataset_final_report.txt

Chạy nhanh:
- cd Preprocessing_Data/Take_A_Sample_To_Run_Local
- python Final_32k_Sampler.py

Ghi chú & gợi ý cải tiến:
- Nếu nhiều category rất nhỏ, 32k có thể thiên về các nhóm lớn khi phân bổ phần dư; có thể thêm ràng buộc “trần” theo tỷ lệ để giữ đa dạng
- Có thể thêm vòng phân bổ lặp lại (multi-round allocation) để gần hơn với chiến lược "đi hết lượt mỗi label rồi quay vòng" nếu muốn
- Kiểm tra URL (HEAD/GET) trước khi chốt mẫu giúp tránh mẫu chết khi train
- Cho huấn luyện local rất nhanh, balanced 25 mẫu/category thường cho hiệu quả tốt hơn về tổng thể so với 32k thiên lệch
