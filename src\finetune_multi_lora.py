#!/usr/bin/env python3
"""
Multi-LoRA Finetuning Module for VLM Model

This module contains Multi-LoRA architecture and training utilities for multi-task learning:
- Task-specific LoRA adapters
- Shared backbone with specialized heads
- Efficient parameter sharing
- Dynamic task routing

Consolidated from: Multi_LoRA_Finetune/
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model, TaskType
import logging
from typing import Dict, List, Optional, Tuple, Any
import math
import numpy as np
from dataclasses import dataclass
import json
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class MultiLoRAConfig:
    """Configuration for Multi-LoRA setup"""
    
    base_rank: int = 32
    food_rank: int = 64      # Higher rank for food task (more complex)
    harmful_rank: int = 32   # Lower rank for harmful task (binary)
    alpha_ratio: float = 2.0 # alpha = rank * alpha_ratio
    dropout: float = 0.1
    target_modules: Optional[List[str]] = None
    
    def __post_init__(self):
        if self.target_modules is None:
            # Target modules for Vintern VLM
            self.target_modules = [
                "q_proj", "k_proj", "v_proj", "o_proj",  # Attention layers
                "gate_proj", "up_proj", "down_proj",     # MLP layers
                "embed_tokens", "lm_head"                # Embedding layers
            ]
    
    def get_food_config(self) -> LoraConfig:
        """LoRA config for food classification task"""
        return LoraConfig(
            r=self.food_rank,
            lora_alpha=int(self.food_rank * self.alpha_ratio),
            target_modules=self.target_modules,
            lora_dropout=self.dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM
        )
    
    def get_harmful_config(self) -> LoraConfig:
        """LoRA config for harmful content detection task"""
        return LoraConfig(
            r=self.harmful_rank,
            lora_alpha=int(self.harmful_rank * self.alpha_ratio),
            target_modules=self.target_modules,
            lora_dropout=self.dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM
        )


class TaskSpecificHead(nn.Module):
    """Task-specific classification head"""
    
    def __init__(self, hidden_size: int, num_classes: int, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_size, num_classes)
        self.num_classes = num_classes
        
        # Initialize weights
        nn.init.normal_(self.classifier.weight, std=0.02)
        nn.init.zeros_(self.classifier.bias)
    
    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        hidden_states = self.dropout(hidden_states)
        logits = self.classifier(hidden_states)
        return logits


class MultiLoRAModel(nn.Module):
    """Multi-LoRA model for multi-task learning"""
    
    def __init__(self, 
                 model_name: str,
                 config: MultiLoRAConfig,
                 food_classes: int = 1000,
                 harmful_classes: int = 2):
        super().__init__()
        
        self.config = config
        self.model_name = model_name
        self.food_classes = food_classes
        self.harmful_classes = harmful_classes
        
        # Load base model
        self.base_model = AutoModel.from_pretrained(model_name, trust_remote_code=True)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        
        # Add padding token if not exists
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Get hidden size
        self.hidden_size = self.base_model.config.hidden_size
        
        # Create task-specific heads
        self.food_head = TaskSpecificHead(self.hidden_size, food_classes)
        self.harmful_head = TaskSpecificHead(self.hidden_size, harmful_classes)
        
        # Task routing
        self.task_router = nn.Linear(self.hidden_size, 2)  # 2 tasks
        
        # LoRA adapters will be added later
        self.food_adapter = None
        self.harmful_adapter = None
        
        logger.info(f"MultiLoRA model initialized with {food_classes} food classes, {harmful_classes} harmful classes")
    
    def add_lora_adapters(self):
        """Add LoRA adapters for each task"""
        # Food task adapter
        food_config = self.config.get_food_config()
        self.food_adapter = get_peft_model(self.base_model, food_config, adapter_name="food")
        
        # Harmful task adapter
        harmful_config = self.config.get_harmful_config()
        self.harmful_adapter = get_peft_model(self.base_model, harmful_config, adapter_name="harmful")
        
        logger.info("LoRA adapters added successfully")
    
    def forward(self, 
                input_ids: torch.Tensor,
                attention_mask: torch.Tensor,
                task_type: str = "food",
                labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Forward pass with task-specific routing"""
        
        # Get base model outputs
        if task_type == "food" and self.food_adapter is not None:
            self.food_adapter.set_adapter("food")
            outputs = self.food_adapter(input_ids=input_ids, attention_mask=attention_mask)
        elif task_type == "harmful" and self.harmful_adapter is not None:
            self.harmful_adapter.set_adapter("harmful")
            outputs = self.harmful_adapter(input_ids=input_ids, attention_mask=attention_mask)
        else:
            outputs = self.base_model(input_ids=input_ids, attention_mask=attention_mask)
        
        # Get last hidden state
        hidden_states = outputs.last_hidden_state
        
        # Pool hidden states (mean pooling)
        pooled_output = hidden_states.mean(dim=1)
        
        # Task-specific classification
        if task_type == "food":
            logits = self.food_head(pooled_output)
        elif task_type == "harmful":
            logits = self.harmful_head(pooled_output)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
        
        # Calculate loss if labels provided
        loss = None
        if labels is not None:
            if task_type == "food":
                loss_fct = nn.CrossEntropyLoss()
            else:  # harmful
                loss_fct = nn.BCEWithLogitsLoss()
            loss = loss_fct(logits.view(-1, logits.size(-1)), labels.view(-1))
        
        return {
            "loss": loss,
            "logits": logits,
            "hidden_states": hidden_states,
            "pooled_output": pooled_output
        }


class MultiLoRATrainer:
    """Trainer for Multi-LoRA model"""
    
    def __init__(self, 
                 model: MultiLoRAModel,
                 train_datasets: Dict[str, Any],
                 eval_datasets: Dict[str, Any],
                 output_dir: str = "./multi_lora_results"):
        
        self.model = model
        self.train_datasets = train_datasets
        self.eval_datasets = eval_datasets
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Training history
        self.training_history = {
            "food": {"train_loss": [], "eval_loss": [], "eval_accuracy": []},
            "harmful": {"train_loss": [], "eval_loss": [], "eval_accuracy": []}
        }
    
    def create_training_args(self, task_type: str, **kwargs) -> TrainingArguments:
        """Create training arguments for specific task"""
        default_args = {
            "output_dir": str(self.output_dir / f"{task_type}_checkpoints"),
            "num_train_epochs": 3,
            "per_device_train_batch_size": 8,
            "per_device_eval_batch_size": 16,
            "warmup_steps": 500,
            "weight_decay": 0.01,
            "logging_dir": str(self.output_dir / f"{task_type}_logs"),
            "logging_steps": 100,
            "eval_steps": 500,
            "save_steps": 1000,
            "evaluation_strategy": "steps",
            "save_strategy": "steps",
            "load_best_model_at_end": True,
            "metric_for_best_model": "eval_loss",
            "greater_is_better": False,
            "report_to": None,
        }
        
        # Update with custom arguments
        default_args.update(kwargs)
        
        return TrainingArguments(**default_args)
    
    def train_task(self, task_type: str, **training_kwargs) -> Dict[str, float]:
        """Train model on specific task"""
        logger.info(f"Starting training for {task_type} task")
        
        # Get datasets
        train_dataset = self.train_datasets[task_type]
        eval_dataset = self.eval_datasets[task_type]
        
        # Create training arguments
        training_args = self.create_training_args(task_type, **training_kwargs)
        
        # Create custom trainer class for this task
        class TaskSpecificTrainer(Trainer):
            def __init__(self, task_type, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.task_type = task_type
            
            def compute_loss(self, model, inputs, return_outputs=False):
                labels = inputs.pop("labels")
                outputs = model(**inputs, task_type=self.task_type, labels=labels)
                loss = outputs["loss"]
                return (loss, outputs) if return_outputs else loss
        
        # Initialize trainer
        trainer = TaskSpecificTrainer(
            task_type=task_type,
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
        )
        
        # Train
        train_result = trainer.train()
        
        # Save model
        trainer.save_model()
        
        # Evaluate
        eval_result = trainer.evaluate()
        
        # Update training history
        self.training_history[task_type]["train_loss"].append(train_result.training_loss)
        self.training_history[task_type]["eval_loss"].append(eval_result["eval_loss"])
        
        logger.info(f"Training completed for {task_type} task")
        logger.info(f"Train loss: {train_result.training_loss:.4f}")
        logger.info(f"Eval loss: {eval_result['eval_loss']:.4f}")
        
        return {
            "train_loss": train_result.training_loss,
            "eval_loss": eval_result["eval_loss"],
            "eval_results": eval_result
        }
    
    def train_all_tasks(self, **training_kwargs) -> Dict[str, Dict[str, float]]:
        """Train model on all tasks sequentially"""
        results = {}
        
        # Add LoRA adapters
        self.model.add_lora_adapters()
        
        # Train each task
        for task_type in ["food", "harmful"]:
            if task_type in self.train_datasets:
                results[task_type] = self.train_task(task_type, **training_kwargs)
        
        # Save training history
        self.save_training_history()
        
        return results
    
    def save_training_history(self):
        """Save training history to file"""
        history_file = self.output_dir / "training_history.json"
        with open(history_file, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        logger.info(f"Training history saved to {history_file}")
    
    def save_model(self, save_path: str):
        """Save the complete model"""
        save_path = Path(save_path)
        save_path.mkdir(exist_ok=True)
        
        # Save model
        self.model.save_pretrained(save_path)
        
        # Save tokenizer
        self.model.tokenizer.save_pretrained(save_path)
        
        # Save config
        config_file = save_path / "multi_lora_config.json"
        with open(config_file, 'w') as f:
            json.dump({
                "model_name": self.model.model_name,
                "food_classes": self.model.food_classes,
                "harmful_classes": self.model.harmful_classes,
                "config": {
                    "base_rank": self.model.config.base_rank,
                    "food_rank": self.model.config.food_rank,
                    "harmful_rank": self.model.config.harmful_rank,
                    "alpha_ratio": self.model.config.alpha_ratio,
                    "dropout": self.model.config.dropout,
                    "target_modules": self.model.config.target_modules
                }
            }, f, indent=2)
        
        logger.info(f"Model saved to {save_path}")


# Convenience functions
def create_multi_lora_model(model_name: str, 
                           food_classes: int = 1000,
                           harmful_classes: int = 2,
                           config: Optional[MultiLoRAConfig] = None) -> MultiLoRAModel:
    """Create Multi-LoRA model with default configuration"""
    if config is None:
        config = MultiLoRAConfig()
    
    return MultiLoRAModel(
        model_name=model_name,
        config=config,
        food_classes=food_classes,
        harmful_classes=harmful_classes
    )


def load_multi_lora_model(model_path: str) -> MultiLoRAModel:
    """Load saved Multi-LoRA model"""
    model_path = Path(model_path)
    
    # Load config
    config_file = model_path / "multi_lora_config.json"
    with open(config_file, 'r') as f:
        saved_config = json.load(f)
    
    # Create config object
    config = MultiLoRAConfig(**saved_config["config"])
    
    # Create model
    model = MultiLoRAModel(
        model_name=saved_config["model_name"],
        config=config,
        food_classes=saved_config["food_classes"],
        harmful_classes=saved_config["harmful_classes"]
    )
    
    # Load weights
    model.load_state_dict(torch.load(model_path / "pytorch_model.bin"))
    
    return model


if __name__ == "__main__":
    # Example usage
    config = MultiLoRAConfig(food_rank=64, harmful_rank=32)
    model = create_multi_lora_model(
        model_name="Viet-Mistral/Viet-Mistral-7B-v0.1",
        food_classes=1000,
        harmful_classes=2,
        config=config
    )
    
    print("Multi-LoRA model created successfully!")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
