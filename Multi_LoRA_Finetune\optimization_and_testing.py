#!/usr/bin/env python3
"""
Optimization và Testing utilities cho Multi-LoRA
- Hyperparameter optimization
- Model evaluation và benchmarking
- Performance analysis
- Production readiness testing
"""

import torch
import numpy as np
import json
import logging
import time
from pathlib import Path
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix

from multi_lora_trainer import MultiLoRATrainer
from multi_lora_architecture import create_multi_lora_model

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiLoRAOptimizer:
    """Hyperparameter optimization cho Multi-LoRA"""
    
    def __init__(self, food_data_path, harmful_data_path, base_output_dir="optimization_results"):
        self.food_data_path = food_data_path
        self.harmful_data_path = harmful_data_path
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        self.results = []
    
    def define_search_space(self):
        """Define hyperparameter search space"""
        return {
            'food_ranks': [32, 64, 128],
            'harmful_ranks': [16, 32, 64],
            'learning_rates': [1e-4, 2e-4, 3e-4, 5e-4],
            'batch_sizes': [4, 8, 12, 16],
            'epochs': [8, 10, 12, 15]
        }
    
    def grid_search(self, max_experiments=15):
        """Grid search cho optimal hyperparameters"""
        search_space = self.define_search_space()
        
        # Create combinations (sample if too many)
        import itertools
        combinations = list(itertools.product(
            search_space['food_ranks'],
            search_space['harmful_ranks'],
            search_space['learning_rates'],
            search_space['batch_sizes'],
            search_space['epochs']
        ))
        
        if len(combinations) > max_experiments:
            # Sample randomly
            indices = np.random.choice(len(combinations), max_experiments, replace=False)
            combinations = [combinations[i] for i in indices]
        
        logger.info(f"Starting grid search with {len(combinations)} experiments")
        
        for i, (food_rank, harmful_rank, lr, batch_size, epochs) in enumerate(combinations):
            logger.info(f"\nExperiment {i+1}/{len(combinations)}")
            logger.info(f"Config: food_rank={food_rank}, harmful_rank={harmful_rank}, "
                       f"lr={lr}, batch_size={batch_size}, epochs={epochs}")
            
            result = self._run_experiment(
                food_rank=food_rank,
                harmful_rank=harmful_rank,
                learning_rate=lr,
                batch_size=batch_size,
                num_epochs=epochs,
                experiment_id=f"exp_{i+1}"
            )
            
            self.results.append(result)
            self._save_intermediate_results()
        
        return self.results
    
    def _run_experiment(self, food_rank, harmful_rank, learning_rate, 
                       batch_size, num_epochs, experiment_id):
        """Run single optimization experiment"""
        start_time = time.time()
        
        # Create experiment directory
        exp_dir = self.base_output_dir / experiment_id
        exp_dir.mkdir(exist_ok=True)
        
        config = {
            'food_rank': food_rank,
            'harmful_rank': harmful_rank,
            'learning_rate': learning_rate,
            'batch_size': batch_size,
            'num_epochs': num_epochs
        }
        
        try:
            # Initialize trainer
            trainer = MultiLoRATrainer(output_dir=str(exp_dir))
            
            # Run training
            results = trainer.train(
                food_data_path=self.food_data_path,
                harmful_data_path=self.harmful_data_path,
                num_epochs=num_epochs,
                learning_rate=learning_rate,
                batch_size=batch_size,
                food_rank=food_rank,
                harmful_rank=harmful_rank
            )
            
            # Extract key metrics
            best_val_loss = results['best_val_loss']
            total_time = results['total_time_hours']
            final_accuracies = results['final_metrics']['val_accuracies']
            
            # Compute score (lower is better)
            food_acc = final_accuracies.get('val_food', 0.0)
            harmful_acc = final_accuracies.get('val_harmful', 0.0)
            avg_accuracy = (food_acc + harmful_acc) / 2
            
            # Score combines loss, accuracy, and efficiency
            score = best_val_loss - avg_accuracy + 0.01 * total_time
            
            result = {
                'experiment_id': experiment_id,
                'config': config,
                'best_val_loss': best_val_loss,
                'total_time_hours': total_time,
                'food_accuracy': food_acc,
                'harmful_accuracy': harmful_acc,
                'avg_accuracy': avg_accuracy,
                'score': score,
                'status': 'completed'
            }
            
            logger.info(f"Experiment {experiment_id} completed:")
            logger.info(f"  Val loss: {best_val_loss:.4f}")
            logger.info(f"  Avg accuracy: {avg_accuracy:.3f}")
            logger.info(f"  Time: {total_time:.2f}h")
            
        except Exception as e:
            logger.error(f"Experiment {experiment_id} failed: {e}")
            result = {
                'experiment_id': experiment_id,
                'config': config,
                'best_val_loss': float('inf'),
                'total_time_hours': (time.time() - start_time) / 3600,
                'score': float('inf'),
                'error': str(e),
                'status': 'failed'
            }
        
        return result
    
    def _save_intermediate_results(self):
        """Save intermediate optimization results"""
        results_file = self.base_output_dir / "optimization_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
    
    def analyze_results(self):
        """Analyze optimization results"""
        if not self.results:
            return {}
        
        # Filter successful experiments
        successful = [r for r in self.results if r['status'] == 'completed']
        
        if not successful:
            return {'message': 'No successful experiments'}
        
        # Sort by score (lower is better)
        successful.sort(key=lambda x: x['score'])
        
        best = successful[0]
        
        # Analyze parameter importance
        param_analysis = self._analyze_parameter_importance(successful)
        
        analysis = {
            'total_experiments': len(self.results),
            'successful_experiments': len(successful),
            'best_experiment': best,
            'top_5_experiments': successful[:5],
            'parameter_analysis': param_analysis,
            'recommendations': self._generate_recommendations(successful)
        }
        
        # Save analysis
        with open(self.base_output_dir / "optimization_analysis.json", 'w') as f:
            json.dump(analysis, f, indent=2)
        
        return analysis
    
    def _analyze_parameter_importance(self, results):
        """Analyze which parameters matter most"""
        param_importance = {}
        
        # Group by parameter values
        for param in ['food_rank', 'harmful_rank', 'learning_rate', 'batch_size']:
            param_groups = defaultdict(list)
            
            for result in results:
                param_value = result['config'][param]
                param_groups[param_value].append(result['score'])
            
            # Compute average score for each parameter value
            param_avgs = {k: np.mean(v) for k, v in param_groups.items()}
            param_importance[param] = param_avgs
        
        return param_importance
    
    def _generate_recommendations(self, results):
        """Generate optimization recommendations"""
        if len(results) < 3:
            return ["Need more experiments for reliable recommendations"]
        
        # Find best parameter combinations
        best_configs = [r['config'] for r in results[:3]]
        
        recommendations = []
        
        # Food rank recommendations
        food_ranks = [c['food_rank'] for c in best_configs]
        most_common_food_rank = max(set(food_ranks), key=food_ranks.count)
        recommendations.append(f"Recommended food_rank: {most_common_food_rank}")
        
        # Harmful rank recommendations
        harmful_ranks = [c['harmful_rank'] for c in best_configs]
        most_common_harmful_rank = max(set(harmful_ranks), key=harmful_ranks.count)
        recommendations.append(f"Recommended harmful_rank: {most_common_harmful_rank}")
        
        # Learning rate recommendations
        lrs = [c['learning_rate'] for c in best_configs]
        avg_lr = np.mean(lrs)
        recommendations.append(f"Recommended learning_rate: {avg_lr:.2e}")
        
        # Batch size recommendations
        batch_sizes = [c['batch_size'] for c in best_configs]
        most_common_batch = max(set(batch_sizes), key=batch_sizes.count)
        recommendations.append(f"Recommended batch_size: {most_common_batch}")
        
        return recommendations

class MultiLoRAEvaluator:
    """Comprehensive evaluation cho Multi-LoRA models"""
    
    def __init__(self, model_path, device='cuda'):
        self.model_path = model_path
        self.device = device
        self.model = None
    
    def load_model(self, food_categories):
        """Load trained Multi-LoRA model"""
        logger.info(f"Loading model from {self.model_path}")
        
        # Load metadata
        metadata_path = Path(self.model_path) / "model_metadata.json"
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        # Create model
        self.model = create_multi_lora_model(
            "Vintern-1B-v3_5",  # Base model
            food_categories,
            food_rank=metadata['multi_lora_config']['food_rank'],
            harmful_rank=metadata['multi_lora_config']['harmful_rank'],
            device=self.device
        )
        
        # Load trained weights
        self.model.load_lora_adapters(self.model_path)
        
        logger.info("Model loaded successfully")
    
    def evaluate_on_test_set(self, test_loader):
        """Comprehensive evaluation on test set"""
        self.model.eval()
        
        all_predictions = []
        all_labels = []
        all_task_types = []
        all_confidences = []
        
        with torch.no_grad():
            for batch in test_loader:
                images = batch['images'].to(self.device)
                task_types = batch['task_types']
                labels = batch['labels']
                
                # Get predictions
                predictions, confidences = self.model.predict(images, task_types)
                
                all_predictions.extend(predictions)
                all_labels.extend(labels)
                all_task_types.extend(task_types)
                all_confidences.extend(confidences)
        
        # Separate by task
        food_preds = [p for p, t in zip(all_predictions, all_task_types) if t == 'food_classification']
        food_labels = [l for l, t in zip(all_labels, all_task_types) if t == 'food_classification']
        food_confs = [c for c, t in zip(all_confidences, all_task_types) if t == 'food_classification']
        
        harmful_preds = [p for p, t in zip(all_predictions, all_task_types) if t == 'harmful_detection']
        harmful_labels = [l for l, t in zip(all_labels, all_task_types) if t == 'harmful_detection']
        harmful_confs = [c for c, t in zip(all_confidences, all_task_types) if t == 'harmful_detection']
        
        # Compute metrics
        evaluation_results = {
            'food_classification': self._compute_task_metrics(food_preds, food_labels, food_confs),
            'harmful_detection': self._compute_task_metrics(harmful_preds, harmful_labels, harmful_confs),
            'overall': {
                'total_samples': len(all_predictions),
                'avg_confidence': np.mean(all_confidences)
            }
        }
        
        return evaluation_results
    
    def _compute_task_metrics(self, predictions, labels, confidences):
        """Compute metrics cho single task"""
        if not predictions:
            return {}
        
        # Accuracy
        accuracy = np.mean([p == l for p, l in zip(predictions, labels)])
        
        # Confidence statistics
        conf_stats = {
            'mean_confidence': np.mean(confidences),
            'std_confidence': np.std(confidences),
            'min_confidence': np.min(confidences),
            'max_confidence': np.max(confidences)
        }
        
        # Classification report
        try:
            from sklearn.metrics import classification_report
            report = classification_report(labels, predictions, output_dict=True)
        except:
            report = {}
        
        return {
            'accuracy': accuracy,
            'num_samples': len(predictions),
            'confidence_stats': conf_stats,
            'classification_report': report
        }
    
    def benchmark_inference_speed(self, test_loader, num_batches=10):
        """Benchmark inference speed"""
        self.model.eval()
        
        times = []
        
        with torch.no_grad():
            for i, batch in enumerate(test_loader):
                if i >= num_batches:
                    break
                
                images = batch['images'].to(self.device)
                task_types = batch['task_types']
                
                start_time = time.time()
                predictions, confidences = self.model.predict(images, task_types)
                end_time = time.time()
                
                batch_time = end_time - start_time
                times.append(batch_time)
        
        avg_time_per_batch = np.mean(times)
        avg_time_per_sample = avg_time_per_batch / len(batch['images'])
        
        return {
            'avg_time_per_batch': avg_time_per_batch,
            'avg_time_per_sample': avg_time_per_sample,
            'samples_per_second': 1.0 / avg_time_per_sample,
            'total_batches_tested': len(times)
        }

def run_optimization(food_data_path, harmful_data_path, max_experiments=12):
    """Run hyperparameter optimization"""
    logger.info("Starting Multi-LoRA hyperparameter optimization...")
    
    optimizer = MultiLoRAOptimizer(food_data_path, harmful_data_path)
    results = optimizer.grid_search(max_experiments=max_experiments)
    analysis = optimizer.analyze_results()
    
    logger.info("Optimization completed!")
    logger.info("Best configuration:")
    if 'best_experiment' in analysis:
        best_config = analysis['best_experiment']['config']
        for param, value in best_config.items():
            logger.info(f"  {param}: {value}")
    
    logger.info("Recommendations:")
    for rec in analysis.get('recommendations', []):
        logger.info(f"  - {rec}")
    
    return analysis

def run_evaluation(model_path, test_loader):
    """Run comprehensive model evaluation"""
    logger.info("Starting Multi-LoRA model evaluation...")
    
    # Load food categories (you'll need to provide this)
    # For now, using placeholder
    food_categories = ['placeholder']  # Replace with actual categories
    
    evaluator = MultiLoRAEvaluator(model_path)
    evaluator.load_model(food_categories)
    
    # Evaluate on test set
    eval_results = evaluator.evaluate_on_test_set(test_loader)
    
    # Benchmark speed
    speed_results = evaluator.benchmark_inference_speed(test_loader)
    
    logger.info("Evaluation completed!")
    logger.info(f"Food classification accuracy: {eval_results['food_classification']['accuracy']:.3f}")
    logger.info(f"Harmful detection accuracy: {eval_results['harmful_detection']['accuracy']:.3f}")
    logger.info(f"Inference speed: {speed_results['samples_per_second']:.1f} samples/sec")
    
    return eval_results, speed_results

if __name__ == "__main__":
    # Example usage
    food_data_path = "../Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_DATASET.csv"
    harmful_data_path = "harmful_dataset.csv"
    
    # Run optimization
    optimization_results = run_optimization(food_data_path, harmful_data_path, max_experiments=8)
    
    print("Optimization completed. Check optimization_results/ for detailed results.")
