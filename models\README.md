# Models Directory

This directory contains trained models and their associated files.

## Structure

```
models/
├── vintern_v3_5/          # VinternV3.5 model files
│   ├── model.pth          # Model weights
│   ├── config.json        # Model configuration
│   └── results.json       # Training/testing results
└── README.md              # This file
```

## Model Information

### VinternV3.5
- **Description**: Fine-tuned vision-language model
- **Architecture**: Based on VinternV3.5
- **Training Method**: Progressive and Multi-LoRA finetuning
- **Performance**: See results.json for detailed metrics

## Usage

```python
from src.test_methods import load_model

# Load the model
model = load_model('models/vintern_v3_5/model.pth')
```

## Notes

- Large model files (>100MB) are stored using Git LFS
- Do not commit untrained or temporary model files
- Always include results.json with performance metrics
