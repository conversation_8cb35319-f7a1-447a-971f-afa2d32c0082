## Vietnamese Food VLM — Tổng thể dự án

Dự án xây dựng pipeline hoàn chỉnh: tiền xử lý dữ liệu (hybrid rules + AI 2 vòng) → lấy mẫu cho huấn luyện local → hu<PERSON><PERSON> luyện Progressive (local/server) → U<PERSON> kiểm thử. Kèm theo bộ thử nghiệm nhiều phương pháp finetune và bảng xếp hạng.

---

## Kiến trúc & Flow

1) Preprocessing_Data — Tiền xử lý & phân loại dữ liệu (Rules-based + AI Round1/2) rồi hợp nhất
2) Preprocessing_Data/Take_A_Sample_To_Run_Local — Lấy mẫu cân bằng 25 mẫu/category và/hoặc 32k mẫu
3) Progressive_Method_Finetune — Huấn luyện progressive
   - Local: dùng tập sample
   - Server (L4): dùng full dataset 700k, có checkpoint/resume
4) Method_For_Test_With_Model_VinternV3.5 — <PERSON><PERSON> thử nghiệm nhiều phương pháp finetune (LoRA/QLoRA/Adapter/...)
5) UI_For_VLM — Ứng dụng UI (Flask + HTML) để test model đã fine-tune

Sơ đồ luồng ngắn gọn:
- Original_Data → Preprocessing_Data (Rules → AI R1 → AI R2 → Combine) → FINAL_ULTIMATE_COMPLETE_DATASET.csv
- (Nhánh A) Take sample 25/category → Progressive training Local
- (Nhánh B) Dùng full dataset → Progressive training Server (L4)
- Model đã fine-tune → UI_For_VLM để kiểm thử

---

## README con (đi tới):
- Preprocessing_Data/README.md — pipeline tiền xử lý, thiết kế Rules/AI, số liệu, cách chạy, biểu đồ
- Preprocessing_Data/Take_A_Sample_To_Run_Local/README.md — lấy mẫu 25/category & 32k
- Progressive_Method_Finetune/README.md — hướng dẫn train local/server, tham số, checkpoint
- Method_For_Test_With_Model_VinternV3.5/README.md — mô tả các phương pháp đã thử + bảng xếp hạng
- UI_For_VLM/README.md — chạy backend/frontend, nạp model đã fine-tune và test

---

## Các file/báo cáo quan trọng
- FINAL_ULTIMATE_COMPLETE_REPORT.md — tổng quan kỹ thuật & kết quả phân loại (92.4%, 1,415 categories)
- FINAL_ULTIMATE_COMPLETE_ANALYSIS.json — số liệu thống kê hợp nhất để vẽ biểu đồ
- VLM_TRAINING_PREPARATION_REPORT.md — báo cáo tạo tập train cân bằng

---

## Rủi ro tiềm tàng & đề xuất cải tiến

- Phân phối lệch: rất nhiều category nhỏ/siêu nhỏ → cân nhắc class weighting hoặc sampler nâng cao khi train
- Nhãn AI đặt tên cụm: có thể lệch ngữ nghĩa/over-merge → nên có vòng review bán tự động (active learning)
- Ảnh/URL chết: cần routine kiểm tra/refresh URL trước khi train server quy mô lớn
- Proxy loss khi dùng model.chat: cân nhắc thay bằng supervised loss nếu có ground-truth câu trả lời/caption để ổn định hơn
- Ontology: chuẩn hóa nhiều tầng để hỗ trợ evaluation theo cấp (super-group → group → label)

Cải tiến đề xuất:
- Thêm voting có trọng số giữa Keyword/AI R1/AI R2 theo confidence
- Tự động phát hiện outlier/điểm nhiễu trong cluster AI
- Thêm augment ảnh nhẹ + mixup/cutmix cho bài toán recognition nếu cần
- Logging & MLflow/wandb để theo dõi thí nghiệm
- Docker hóa UI + backend để triển khai nhanh

---

## Cách bắt đầu nhanh

1) Hợp nhất dữ liệu cuối: Preprocessing_Data/README.md (mục 6) → tạo FINAL_ULTIMATE_COMPLETE_DATASET.csv
2) Lấy balanced sample 25/category hoặc 32k: Preprocessing_Data/Take_A_Sample_To_Run_Local/README.md
3) Train progressive:
   - Local: Progressive_Method_Finetune/README.md (local)
   - Server: Progressive_Method_Finetune/README.md (server L4)
4) Khởi chạy UI để test: UI_For_VLM/README.md

---

## Bảng xếp hạng phương pháp (tóm tắt)
Xem chi tiết: Method_For_Test_With_Model_VinternV3.5/README.md

- 1 Progressive Training — Accuracy 93–96%, Improvement 78.0%
- 2 Multi-Modal Adapter — 92–95%, +83.8%
- 3 LIT — 92–95%, +77.5%
- 4 SAM LoRA — 88–91%, +87.1%
- 5 VRGAdapter — 90–93%, +80.2%
- 6 CLIP-LoRA — 87–90%, +94.5%
- 7 DoRA — 93–96%, +82.2%
- 8 ClipFit — 86–89%, +94.7%
- 9 EHFR-Net — 93–95%, +84.5%
- 10 Visual Prompt Tuning — 81–84%, +96.0%

---

## Trạng thái
- Code & tài liệu đã được sắp xếp theo flow
- Đề nghị bổ sung test unit/integ cho: sampler, split, URL validator, các collate_fn/transform
- Có thể thêm notebook demo để dựng biểu đồ từ ANALYSIS.json

