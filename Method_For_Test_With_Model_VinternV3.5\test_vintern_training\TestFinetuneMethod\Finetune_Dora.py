#!/usr/bin/env python3
"""
APPROACH 9: <PERSON><PERSON> (Weight-Decomposed LoRA)
Decompose weights into magnitude and direction for better optimization
"""

import os
import torch
import pandas as pd
import logging
import random
import math
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DoRADataset(Dataset):
    """
    Dataset cho DoRA training
    Focus vào balanced examples để test weight decomposition
    """
    def __init__(self, df, tokenizer, max_length=125):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.dora_data = self.create_balanced_examples()
        
        logger.info(f"Tạo DoRADataset với {len(self.dora_data)} balanced examples")
    
    def create_balanced_examples(self):
        """Tạo balanced examples cho weight decomposition"""
        
        # Balanced templates for magnitude/direction learning
        magnitude_templates = [
            # Short magnitude (low magnitude)
            ("{food_name}", "short"),
            ("Món {food_name}", "basic"),
            
            # Medium magnitude
            ("Tên món: {food_name}", "medium"),
            ("Đây là: {food_name}", "descriptive"),
            
            # High magnitude (complex patterns)
            ("Món ăn truyền thống: {food_name}", "traditional"),
            ("Ẩm thực Việt Nam: {food_name}", "cultural")
        ]
        
        dora_data = []
        
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            
            if len(food_name) < 3 or len(food_name) > 20:
                continue
            
            # Calculate magnitude based on food characteristics
            magnitude_score = self.calculate_magnitude_score(food_name)
            
            # Select template based on magnitude
            if magnitude_score < 20:
                template_type = "short"
                available_templates = [t for t in magnitude_templates if t[1] == "short"]
            elif magnitude_score < 40:
                template_type = "medium"
                available_templates = [t for t in magnitude_templates if t[1] in ["basic", "medium"]]
            else:
                template_type = "high"
                available_templates = [t for t in magnitude_templates if t[1] in ["descriptive", "traditional", "cultural"]]
            
            # Create examples
            for template, template_category in available_templates:
                text = template.format(food_name=food_name)
                
                dora_data.append({
                    'food_name': food_name,
                    'template_type': template_type,
                    'template_category': template_category,
                    'text': text,
                    'magnitude_score': magnitude_score,
                    'direction_vector': self.calculate_direction_vector(food_name)
                })
        
        # Balance dataset by magnitude
        dora_data.sort(key=lambda x: (x['magnitude_score'], x['direction_vector']))
        
        return dora_data
    
    def calculate_magnitude_score(self, food_name):
        """Calculate magnitude score for weight decomposition"""
        score = 0
        
        # Length-based magnitude
        score += len(food_name) * 2
        score += len(food_name.split()) * 5
        
        # Complexity-based magnitude
        if any(char in food_name for char in ['ă', 'â', 'ê', 'ô', 'ơ', 'ư']):
            score += 10  # Vietnamese diacritics
        
        # Cultural significance
        cultural_words = ['phở', 'bún', 'bánh', 'chè', 'nem', 'gỏi', 'cơm']
        if any(word in food_name for word in cultural_words):
            score += 15
        
        return min(score, 60)  # Cap at 60
    
    def calculate_direction_vector(self, food_name):
        """Calculate direction vector for weight decomposition"""
        # Simple hash-based direction
        direction = sum(ord(char) for char in food_name) % 100
        return direction / 100.0  # Normalize to [0, 1]
    
    def __len__(self):
        return len(self.dora_data)
    
    def __getitem__(self, idx):
        item = self.dora_data[idx]
        text = item['text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'magnitude_score': item['magnitude_score'],
            'direction_vector': item['direction_vector']
        }

def setup_dora_model(model):
    """
    Setup DoRA-inspired LoRA với weight decomposition principles
    """
    logger.info("=== THIẾT LẬP DORA ===")
    
    # Freeze toàn bộ model
    for param in model.parameters():
        param.requires_grad = False
    
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        # DoRA-inspired config
        dora_config = LoraConfig(
            r=36,                    # Balanced rank for decomposition
            lora_alpha=72,           # 2x rank for magnitude/direction balance
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.03,       # Low dropout for stable decomposition
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        model.language_model = get_peft_model(llm, dora_config)
        logger.info("✅ DoRA-inspired LoRA applied")
        model.language_model.print_trainable_parameters()
    
    return model

def magnitude_direction_loss(outputs, magnitude_scores, direction_vectors):
    """
    Custom loss function inspired by DoRA weight decomposition
    """
    base_loss = outputs.loss
    
    # Magnitude regularization
    avg_magnitude = torch.mean(magnitude_scores.float())
    magnitude_reg = torch.abs(avg_magnitude - 30.0) / 30.0  # Target magnitude = 30
    
    # Direction consistency
    direction_var = torch.var(direction_vectors.float())
    direction_reg = direction_var  # Encourage diversity
    
    # Combined loss
    total_loss = base_loss + 0.1 * magnitude_reg + 0.05 * direction_reg
    
    return total_loss, base_loss.item(), magnitude_reg.item(), direction_reg.item()

def train_dora_model(model, train_dataloader, device, num_epochs=1):
    """Training DoRA với weight decomposition techniques"""
    logger.info("=== BẮT ĐẦU DORA TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    # DoRA optimizer với weight decomposition in mind
    optimizer = torch.optim.AdamW(
        trainable_params, 
        lr=1.5e-4,                 # Moderate LR for stable decomposition
        weight_decay=0.02,         # Higher weight decay for regularization
        betas=(0.9, 0.95)
    )
    
    # Cosine annealing for smooth convergence
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=len(train_dataloader) * num_epochs
    )
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    max_steps = 100  # Balanced steps for DoRA
    
    magnitude_losses = []
    direction_losses = []
    
    for epoch in range(num_epochs):
        logger.info(f"--- DORA EPOCH {epoch + 1}/{num_epochs} ---")
        
        for batch in tqdm(train_dataloader, desc=f"DoRA Training"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            magnitude_scores = batch['magnitude_score']
            direction_vectors = batch['direction_vector']
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                # DoRA-inspired loss
                total_loss_val, base_loss, mag_reg, dir_reg = magnitude_direction_loss(
                    outputs, magnitude_scores, direction_vectors
                )
                
                total_loss += total_loss_val.item()
                magnitude_losses.append(mag_reg)
                direction_losses.append(dir_reg)
                
                optimizer.zero_grad()
                total_loss_val.backward()
                
                # Gradient clipping for stable decomposition
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=0.8)
                
                optimizer.step()
                scheduler.step()
                
                step += 1
                
                if step % 20 == 0:
                    avg_loss = total_loss / step
                    avg_mag_loss = sum(magnitude_losses[-10:]) / len(magnitude_losses[-10:])
                    avg_dir_loss = sum(direction_losses[-10:]) / len(direction_losses[-10:])
                    current_lr = scheduler.get_last_lr()[0]
                    
                    logger.info(f"Step {step}/{max_steps} - Total Loss: {total_loss_val.item():.4f} - Avg Loss: {avg_loss:.4f}")
                    logger.info(f"  Base: {base_loss:.4f} - Magnitude Reg: {avg_mag_loss:.4f} - Direction Reg: {avg_dir_loss:.4f} - LR: {current_lr:.6f}")
                
                training_history.append({
                    'step': step,
                    'total_loss': total_loss_val.item(),
                    'base_loss': base_loss,
                    'magnitude_reg': mag_reg,
                    'direction_reg': dir_reg,
                    'avg_loss': total_loss / step,
                    'lr': scheduler.get_last_lr()[0]
                })
                
            except Exception as e:
                logger.error(f"DoRA training error: {str(e)}")
                step += 1
                continue
        
        if step >= max_steps:
            break
    
    logger.info("✅ DoRA training completed!")
    return model, training_history

def test_dora_model(model, tokenizer, device):
    """Test DoRA model với magnitude/direction awareness"""
    logger.info("=== TESTING DORA MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test prompts với different magnitude levels
    test_cases = [
        # Low magnitude
        ("Món", "low_mag"),
        ("Tên", "low_mag"),
        
        # Medium magnitude
        ("Tên món:", "med_mag"),
        ("Đây là:", "med_mag"),
        
        # High magnitude
        ("Món ăn truyền thống:", "high_mag"),
        ("Ẩm thực Việt Nam:", "high_mag")
    ]
    
    test_results = []
    
    for prompt, magnitude_level in test_cases:
        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 12,
                    temperature=0.4,  # Lower temperature for stable decomposition
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            
            test_results.append({
                'prompt': prompt,
                'magnitude_level': magnitude_level,
                'generated': generated
            })
            
            logger.info(f"[{magnitude_level}] '{prompt}' → '{generated}'")
            
        except Exception as e:
            logger.warning(f"DoRA generation failed: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 9: DORA")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach9_dora_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Setup DoRA
    model = setup_dora_model(model)
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(150).dropna(subset=['food_name'])
    
    # Create DoRA dataset
    train_dataset = DoRADataset(train_df, tokenizer)
    train_dataloader = DataLoader(train_dataset, batch_size=2, shuffle=True)
    
    logger.info(f"DoRA examples: {len(train_dataset)}")
    
    # Training
    model, history = train_dora_model(model, train_dataloader, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_dora_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'DoRA (Weight-Decomposed LoRA)',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'dora_examples': len(train_dataset),
        'weight_decomposition': True
    }
    
    with open('output_dora.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 9: DORA RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"DoRA examples: {results['dora_examples']}\n")
        f.write(f"Weight decomposition: {results['weight_decomposition']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']}: Total Loss={entry['total_loss']:.4f}, Base Loss={entry['base_loss']:.4f}\n")
        
        f.write("\nDoRA Test Results:\n")
        for result in test_results:
            f.write(f"[{result['magnitude_level']}] '{result['prompt']}' → '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 9 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_dora.txt")

if __name__ == "__main__":
    main()
