#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> tổng hợp để chạy improved training v<PERSON>i tất cả cải tiến
- Improved loss functions
- Enhanced hyperparameters  
- Advanced techniques
- Auto model selection
"""

import argparse
import logging
import json
import time
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='Run Improved Vintern Training')
    parser.add_argument('--mode', choices=['local', 'server', 'search'], default='local',
                       help='Training mode')
    parser.add_argument('--epochs', type=int, default=4,
                       help='Epochs per level')
    parser.add_argument('--model_path', type=str, default='Vintern-1B-v3_5',
                       help='Base model path')
    parser.add_argument('--output_dir', type=str, default='improved_training_results',
                       help='Output directory')
    parser.add_argument('--techniques', nargs='+', 
                       choices=['improved_loss', 'advanced_aug', 'knowledge_distill', 'active_learning'],
                       default=['improved_loss'],
                       help='Advanced techniques to use')
    
    args = parser.parse_args()
    
    logger.info("="*60)
    logger.info("IMPROVED VINTERN TRAINING")
    logger.info("="*60)
    logger.info(f"Mode: {args.mode}")
    logger.info(f"Epochs per level: {args.epochs}")
    logger.info(f"Techniques: {args.techniques}")
    logger.info("="*60)
    
    if args.mode == 'local':
        run_improved_local_training(args)
    elif args.mode == 'server':
        run_server_training(args)
    elif args.mode == 'search':
        run_hyperparameter_search(args)

def run_improved_local_training(args):
    """Run improved local training"""
    logger.info("Starting Improved Local Training...")
    
    try:
        from FinetuneWithProgressInLocal.ImprovedFinetuneMethod import ImprovedProgressiveTrainer
        
        trainer = ImprovedProgressiveTrainer(
            model_path=args.model_path,
            output_dir=args.output_dir
        )
        
        results = trainer.enhanced_progressive_train(
            num_epochs_per_level=args.epochs
        )
        
        logger.info("✅ Improved local training completed!")
        logger.info(f"Best loss: {results['best_overall_loss']:.4f}")
        logger.info(f"Time: {results['total_time_hours']:.2f} hours")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Local training failed: {e}")
        return None

def run_server_training(args):
    """Run server training with full dataset"""
    logger.info("Starting Server Training with Full Dataset...")
    
    try:
        from FinetuneWithProgressiveInServer.Finetune_L4_Progressive_method import ServerL4ProgressiveTrainer
        
        trainer = ServerL4ProgressiveTrainer(
            model_path=args.model_path,
            output_dir=args.output_dir
        )
        
        results = trainer.progressive_train_with_checkpoints(
            num_epochs_per_level=args.epochs
        )
        
        logger.info("✅ Server training completed!")
        logger.info(f"Best loss: {results['best_overall_loss']:.4f}")
        logger.info(f"Time: {results['total_time_hours']:.2f} hours")
        logger.info(f"Dataset size: {results['dataset_size']}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Server training failed: {e}")
        return None

def run_hyperparameter_search(args):
    """Run hyperparameter search"""
    logger.info("Starting Hyperparameter Search...")
    
    try:
        from hyperparameter_search import HyperparameterSearch
        
        searcher = HyperparameterSearch(base_output_dir=args.output_dir)
        
        # Run search
        results = searcher.grid_search_basic(max_experiments=10)
        
        # Get best config
        best_config = searcher.get_best_config()
        
        logger.info("✅ Hyperparameter search completed!")
        logger.info("Best configuration:")
        for key, value in best_config.items():
            logger.info(f"  {key}: {value}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Hyperparameter search failed: {e}")
        return None

if __name__ == "__main__":
    main()
