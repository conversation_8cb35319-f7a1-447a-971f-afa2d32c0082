#!/usr/bin/env python3
"""
Analyze FINAL_ULTIMATE_COMPLETE_DATASET.csv and prepare balanced training data
for VLM Vintern v3.5 fine-tuning
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from collections import Counter
import requests
from PIL import Image
import io
import time
import random

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrainingDataPreparator:
    """Prepare balanced training data from classified dataset"""
    
    def __init__(self):
        logger.info("🚀 TRAINING DATA PREPARATION FOR VLM VINTERN V3.5")
        logger.info("="*60)
        
    def analyze_dataset(self, input_file="FINAL_ULTIMATE_COMPLETE_DATASET.csv"):
        """Analyze the classified dataset"""
        logger.info("📊 Analyzing classified dataset...")
        
        try:
            df = pd.read_csv(input_file, encoding='utf-8')
            logger.info(f"📊 Loaded dataset: {len(df):,} items")
            
            # Filter out unclassified items
            classified_df = df[df['final_ultimate_category'] != 'Chưa phân loại'].copy()
            logger.info(f"📊 Classified items: {len(classified_df):,}")
            
            # Analyze categories
            category_counts = classified_df['final_ultimate_category'].value_counts()
            logger.info(f"📊 Total categories: {len(category_counts)}")
            
            # Show top categories
            logger.info(f"\n🏷️ TOP 20 CATEGORIES:")
            for i, (category, count) in enumerate(category_counts.head(20).items(), 1):
                percentage = (count / len(classified_df)) * 100
                logger.info(f"  {i:2d}. {category}: {count:,} ({percentage:.1f}%)")
            
            # Category distribution analysis
            logger.info(f"\n📈 CATEGORY DISTRIBUTION:")
            logger.info(f"Categories with 1-10 items: {len(category_counts[category_counts <= 10])}")
            logger.info(f"Categories with 11-25 items: {len(category_counts[(category_counts > 10) & (category_counts <= 25)])}")
            logger.info(f"Categories with 26-100 items: {len(category_counts[(category_counts > 25) & (category_counts <= 100)])}")
            logger.info(f"Categories with 100+ items: {len(category_counts[category_counts > 100])}")
            
            return classified_df, category_counts
            
        except Exception as e:
            logger.error(f"❌ Failed to analyze dataset: {e}")
            return None, None
    
    def create_balanced_sample(self, df, category_counts, samples_per_category=25):
        """Create balanced sample with specified samples per category"""
        logger.info(f"🎯 Creating balanced sample with {samples_per_category} samples per category...")
        
        balanced_samples = []
        categories_used = 0
        categories_skipped = 0
        
        for category, count in category_counts.items():
            category_data = df[df['final_ultimate_category'] == category].copy()
            
            # Filter items with valid URLs
            valid_items = category_data[
                (category_data['URL'].notna()) & 
                (category_data['URL'].str.startswith('http'))
            ].copy()
            
            if len(valid_items) >= samples_per_category:
                # Sample exactly the required number
                sampled_items = valid_items.sample(n=samples_per_category, random_state=42)
                balanced_samples.append(sampled_items)
                categories_used += 1
            elif len(valid_items) >= 10:  # Minimum threshold
                # Use all available items if less than required but above minimum
                balanced_samples.append(valid_items)
                categories_used += 1
                logger.info(f"  ⚠️ {category}: Only {len(valid_items)} samples (< {samples_per_category})")
            else:
                categories_skipped += 1
                logger.info(f"  ❌ {category}: Only {len(valid_items)} valid samples (< 10, skipped)")
        
        if balanced_samples:
            balanced_df = pd.concat(balanced_samples, ignore_index=True)
            logger.info(f"✅ Balanced dataset created:")
            logger.info(f"  📊 Categories used: {categories_used}")
            logger.info(f"  📊 Categories skipped: {categories_skipped}")
            logger.info(f"  📊 Total samples: {len(balanced_df):,}")
            
            return balanced_df
        else:
            logger.error("❌ No balanced samples could be created")
            return None
    
    def validate_image_urls(self, df, max_validation=1000):
        """Validate a sample of image URLs"""
        logger.info(f"🔍 Validating sample of {max_validation} image URLs...")
        
        # Sample URLs for validation
        sample_df = df.sample(n=min(max_validation, len(df)), random_state=42)
        
        valid_urls = 0
        invalid_urls = 0
        
        for idx, row in sample_df.iterrows():
            try:
                response = requests.head(row['URL'], timeout=5)
                if response.status_code == 200:
                    valid_urls += 1
                else:
                    invalid_urls += 1
            except:
                invalid_urls += 1
            
            # Progress update
            if (valid_urls + invalid_urls) % 100 == 0:
                logger.info(f"  Validated {valid_urls + invalid_urls}/{len(sample_df)} URLs...")
        
        validation_rate = (valid_urls / len(sample_df)) * 100
        logger.info(f"✅ URL validation complete:")
        logger.info(f"  📊 Valid URLs: {valid_urls} ({validation_rate:.1f}%)")
        logger.info(f"  📊 Invalid URLs: {invalid_urls}")
        
        return validation_rate
    
    def prepare_training_format(self, df, output_dir="vintern_training_data"):
        """Prepare data in format suitable for VLM training"""
        logger.info("🔄 Preparing data for VLM training...")
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Prepare training data format
        training_data = []
        
        for idx, row in df.iterrows():
            # Create training sample
            sample = {
                'image_url': row['URL'],
                'food_name': row['food_name'],
                'category': row['final_ultimate_category'],
                'normalized_name': row['normalized_name'],
                'is_combo': row.get('is_combo', False),
                'classification_method': row.get('classification_method', 'Unknown')
            }
            training_data.append(sample)
        
        # Save training data
        training_file = output_path / "training_data.json"
        with open(training_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)
        
        # Save balanced CSV
        balanced_csv = output_path / "balanced_training_dataset.csv"
        df.to_csv(balanced_csv, index=False, encoding='utf-8')
        
        # Create category mapping
        categories = sorted(df['final_ultimate_category'].unique())
        category_mapping = {category: idx for idx, category in enumerate(categories)}
        
        mapping_file = output_path / "category_mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(category_mapping, f, indent=2, ensure_ascii=False)
        
        # Create training statistics
        category_counts = df['final_ultimate_category'].value_counts()
        stats = {
            'total_samples': int(len(df)),
            'total_categories': int(len(categories)),
            'samples_per_category': {str(k): int(v) for k, v in category_counts.to_dict().items()},
            'category_mapping': category_mapping,
            'data_distribution': {
                'min_samples_per_category': int(category_counts.min()),
                'max_samples_per_category': int(category_counts.max()),
                'avg_samples_per_category': float(category_counts.mean())
            }
        }
        
        stats_file = output_path / "training_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Training data prepared:")
        logger.info(f"  📁 Output directory: {output_path}")
        logger.info(f"  📊 Total samples: {len(training_data):,}")
        logger.info(f"  📊 Total categories: {len(categories)}")
        logger.info(f"  📄 Files created:")
        logger.info(f"    - training_data.json")
        logger.info(f"    - balanced_training_dataset.csv")
        logger.info(f"    - category_mapping.json")
        logger.info(f"    - training_statistics.json")
        
        return training_data, categories, category_mapping, stats
    
    def run_preparation(self, samples_per_category=25):
        """Run complete data preparation pipeline"""
        logger.info("🚀 STARTING TRAINING DATA PREPARATION")
        logger.info("="*60)
        
        start_time = time.time()
        
        try:
            # 1. Analyze dataset
            df, category_counts = self.analyze_dataset()
            if df is None:
                return None
            
            # 2. Create balanced sample
            balanced_df = self.create_balanced_sample(df, category_counts, samples_per_category)
            if balanced_df is None:
                return None
            
            # 3. Validate URLs (sample)
            validation_rate = self.validate_image_urls(balanced_df, max_validation=500)
            
            # 4. Prepare training format
            training_data, categories, category_mapping, stats = self.prepare_training_format(balanced_df)
            
            # 5. Final summary
            total_time = time.time() - start_time
            logger.info(f"\n🎉 TRAINING DATA PREPARATION COMPLETED in {total_time/60:.1f} minutes!")
            logger.info(f"📊 FINAL SUMMARY:")
            logger.info(f"  📊 Total categories: {len(categories)}")
            logger.info(f"  📊 Total training samples: {len(training_data):,}")
            logger.info(f"  📊 URL validation rate: {validation_rate:.1f}%")
            logger.info(f"  📊 Average samples per category: {stats['data_distribution']['avg_samples_per_category']:.1f}")
            logger.info(f"  📁 Ready for VLM training!")
            
            return {
                'training_data': training_data,
                'categories': categories,
                'category_mapping': category_mapping,
                'stats': stats,
                'balanced_df': balanced_df
            }
            
        except Exception as e:
            logger.error(f"❌ Training data preparation failed: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """Main function"""
    try:
        preparator = TrainingDataPreparator()
        
        # Run preparation with 25 samples per category
        result = preparator.run_preparation(samples_per_category=25)
        
        if result:
            logger.info(f"\n🎉 SUCCESS! Training data prepared for VLM Vintern v3.5")
            logger.info(f"📊 Categories: {len(result['categories'])}")
            logger.info(f"📊 Samples: {len(result['training_data']):,}")
            logger.info(f"📁 Data ready in: vintern_training_data/")
            logger.info(f"🚀 Ready for progressive fine-tuning!")
        
    except Exception as e:
        logger.error(f"❌ Main process failed: {e}")

if __name__ == "__main__":
    main()
