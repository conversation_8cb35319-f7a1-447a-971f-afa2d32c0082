#!/usr/bin/env python3
"""
APPROACH 10: Progressive Training (Curriculum Learning)
Train từ examples dễ đến khó theo curriculum learning
"""

import os
import torch
import pandas as pd
import logging
import random
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProgressiveDataset(Dataset):
    """
    Dataset cho Progressive Training
    Organize examples theo difficulty levels
    """
    def __init__(self, df, tokenizer, max_length=135, current_stage="easy"):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.current_stage = current_stage
        self.progressive_data = self.create_progressive_curriculum()
        
        logger.info(f"Tạo ProgressiveDataset với {len(self.progressive_data)} examples (stage: {current_stage})")
    
    def create_progressive_curriculum(self):
        """Tạo curriculum từ dễ đến khó"""
        
        # Curriculum stages
        curriculum = {
            "easy": {
                "templates": [
                    ("{food_name}", 1),
                    ("<PERSON>ón {food_name}", 2)
                ],
                "max_food_length": 10,
                "max_words": 2
            },
            "medium": {
                "templates": [
                    ("Tên món: {food_name}", 3),
                    ("Đây là: {food_name}", 3),
                    ("Nhận diện: {food_name}", 4)
                ],
                "max_food_length": 15,
                "max_words": 3
            },
            "hard": {
                "templates": [
                    ("Món ăn Việt Nam: {food_name}", 5),
                    ("Ẩm thực truyền thống: {food_name}", 6),
                    ("Đặc sản địa phương: {food_name}", 6),
                    ("Món ăn được yêu thích: {food_name}", 5)
                ],
                "max_food_length": 25,
                "max_words": 5
            }
        }
        
        stage_config = curriculum[self.current_stage]
        progressive_data = []
        
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            
            # Filter by stage difficulty
            if len(food_name) > stage_config["max_food_length"]:
                continue
            if len(food_name.split()) > stage_config["max_words"]:
                continue
            if len(food_name) < 3:
                continue
            
            # Calculate difficulty score
            difficulty_score = self.calculate_difficulty(food_name)
            
            # Select appropriate templates for stage
            for template, template_difficulty in stage_config["templates"]:
                text = template.format(food_name=food_name)
                
                total_difficulty = difficulty_score + template_difficulty
                
                progressive_data.append({
                    'food_name': food_name,
                    'text': text,
                    'stage': self.current_stage,
                    'difficulty_score': total_difficulty,
                    'template_difficulty': template_difficulty
                })
        
        # Sort by difficulty for progressive training
        progressive_data.sort(key=lambda x: x['difficulty_score'])
        
        return progressive_data
    
    def calculate_difficulty(self, food_name):
        """Calculate difficulty score cho curriculum"""
        score = 0
        
        # Length-based difficulty
        score += len(food_name)
        score += len(food_name.split()) * 2
        
        # Character complexity
        if any(char in food_name for char in ['ă', 'â', 'ê', 'ô', 'ơ', 'ư']):
            score += 3
        
        # Common vs rare foods
        common_foods = ['cà phê', 'trà', 'nước', 'bánh mì', 'cơm', 'phở']
        if any(common in food_name for common in common_foods):
            score -= 2  # Easier
        
        return max(score, 1)
    
    def update_stage(self, new_stage):
        """Update curriculum stage"""
        self.current_stage = new_stage
        self.progressive_data = self.create_progressive_curriculum()
        logger.info(f"Updated to stage: {new_stage} with {len(self.progressive_data)} examples")
    
    def __len__(self):
        return len(self.progressive_data)
    
    def __getitem__(self, idx):
        item = self.progressive_data[idx]
        text = item['text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'difficulty_score': item['difficulty_score'],
            'stage': item['stage']
        }

def setup_progressive_model(model):
    """Setup LoRA cho Progressive Training"""
    logger.info("=== THIẾT LẬP PROGRESSIVE TRAINING ===")
    
    # Freeze toàn bộ model
    for param in model.parameters():
        param.requires_grad = False
    
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        # Progressive LoRA config - adaptive capacity
        progressive_config = LoraConfig(
            r=28,                    # Medium rank for progressive learning
            lora_alpha=56,           # 2x rank
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.04,       # Moderate dropout
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        model.language_model = get_peft_model(llm, progressive_config)
        logger.info("✅ Progressive LoRA applied")
        model.language_model.print_trainable_parameters()
    
    return model

def train_progressive_model(model, train_df, tokenizer, device, num_epochs=1):
    """Training Progressive với curriculum learning"""
    logger.info("=== BẮT ĐẦU PROGRESSIVE TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    # Progressive optimizer với adaptive learning
    optimizer = torch.optim.AdamW(
        trainable_params, 
        lr=3e-4,                   # Higher initial LR
        weight_decay=0.01,
        betas=(0.9, 0.95)
    )
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    
    # Progressive stages
    stages = ["easy", "medium", "hard"]
    steps_per_stage = 35
    max_steps = len(stages) * steps_per_stage
    
    stage_losses = {"easy": [], "medium": [], "hard": []}
    
    for stage_idx, stage in enumerate(stages):
        logger.info(f"--- PROGRESSIVE STAGE: {stage.upper()} ---")
        
        # Create dataset for current stage
        stage_dataset = ProgressiveDataset(train_df, tokenizer, current_stage=stage)
        stage_dataloader = DataLoader(stage_dataset, batch_size=2, shuffle=True)
        
        # Adjust learning rate for stage
        stage_lr = 3e-4 * (0.8 ** stage_idx)  # Decrease LR for harder stages
        for param_group in optimizer.param_groups:
            param_group['lr'] = stage_lr
        
        stage_step = 0
        
        for batch in tqdm(stage_dataloader, desc=f"Stage {stage}"):
            if stage_step >= steps_per_stage:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            difficulty_scores = batch['difficulty_score']
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                
                # Difficulty-weighted loss
                avg_difficulty = torch.mean(difficulty_scores.float())
                difficulty_weight = 1.0 + (avg_difficulty / 20.0)  # Scale difficulty
                weighted_loss = loss * difficulty_weight
                
                total_loss += weighted_loss.item()
                stage_losses[stage].append(loss.item())
                
                optimizer.zero_grad()
                weighted_loss.backward()
                
                # Progressive gradient clipping
                clip_norm = 0.3 + (stage_idx * 0.2)  # Increase clipping for harder stages
                torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=clip_norm)
                
                optimizer.step()
                
                step += 1
                stage_step += 1
                
                if step % 15 == 0:
                    avg_loss = total_loss / step
                    stage_avg = sum(stage_losses[stage][-5:]) / len(stage_losses[stage][-5:])
                    current_lr = optimizer.param_groups[0]['lr']
                    
                    logger.info(f"Step {step}/{max_steps} [{stage}] - Loss: {weighted_loss.item():.4f} - Stage Avg: {stage_avg:.4f} - LR: {current_lr:.6f}")
                
                training_history.append({
                    'step': step,
                    'stage': stage,
                    'loss': weighted_loss.item(),
                    'base_loss': loss.item(),
                    'avg_loss': total_loss / step,
                    'difficulty_weight': difficulty_weight.item(),
                    'lr': optimizer.param_groups[0]['lr']
                })
                
            except Exception as e:
                logger.error(f"Progressive training error: {str(e)}")
                step += 1
                stage_step += 1
                continue
        
        # Stage completion summary
        if stage_losses[stage]:
            stage_final_loss = sum(stage_losses[stage][-10:]) / len(stage_losses[stage][-10:])
            logger.info(f"✅ Stage {stage} completed - Final avg loss: {stage_final_loss:.4f}")
    
    logger.info("✅ Progressive training completed!")
    return model, training_history

def test_progressive_model(model, tokenizer, device):
    """Test Progressive model với all difficulty levels"""
    logger.info("=== TESTING PROGRESSIVE MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test prompts theo progressive difficulty
    test_cases = [
        # Easy
        ("Món", "easy"),
        ("Tên", "easy"),
        
        # Medium
        ("Tên món:", "medium"),
        ("Đây là:", "medium"),
        ("Nhận diện:", "medium"),
        
        # Hard
        ("Món ăn Việt Nam:", "hard"),
        ("Ẩm thực truyền thống:", "hard"),
        ("Đặc sản địa phương:", "hard")
    ]
    
    test_results = []
    
    for prompt, difficulty in test_cases:
        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 15,
                    temperature=0.5,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            
            test_results.append({
                'prompt': prompt,
                'difficulty': difficulty,
                'generated': generated
            })
            
            logger.info(f"[{difficulty}] '{prompt}' → '{generated}'")
            
        except Exception as e:
            logger.warning(f"Progressive generation failed: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 10: PROGRESSIVE TRAINING")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach10_progressive_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Setup Progressive model
    model = setup_progressive_model(model)
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(180).dropna(subset=['food_name'])
    
    logger.info(f"Progressive training dataset: {len(train_df)} samples")
    
    # Training với curriculum
    model, history = train_progressive_model(model, train_df, tokenizer, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_progressive_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'Progressive Training (Curriculum Learning)',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'curriculum_stages': ["easy", "medium", "hard"],
        'progressive_learning': True
    }
    
    with open('output_progressive.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 10: PROGRESSIVE TRAINING RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"Curriculum stages: {results['curriculum_stages']}\n")
        f.write(f"Progressive learning: {results['progressive_learning']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']} [{entry['stage']}]: Loss={entry['loss']:.4f}, Base Loss={entry['base_loss']:.4f}\n")
        
        f.write("\nProgressive Test Results:\n")
        for result in test_results:
            f.write(f"[{result['difficulty']}] '{result['prompt']}' → '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 10 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_progressive.txt")

if __name__ == "__main__":
    main()
