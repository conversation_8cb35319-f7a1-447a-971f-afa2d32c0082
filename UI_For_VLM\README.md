# 🍜 Vietnamese Food VLM UI Application

**Interactive Web Interface for Vietnamese Food Recognition**

---

## 🎯 Overview

Ứng dụng web interface để test fine-tuned Vietnamese Food VLM model. Người dùng có thể upload ảnh món ăn, nhập câu hỏi, và nhận được dự đoán từ AI model.

### ✨ Features

- **📸 Image Upload:** Drag & drop hoặc click để upload ảnh
- **💬 Text Query:** Nhập câu hỏi về món ăn
- **🤖 AI Prediction:** Nhận dự đoán từ fine-tuned VLM model
- **📊 Confidence Scores:** Hiển thị độ tin cậy của predictions
- **🔄 Model Switching:** C<PERSON> thể thay đổi model mà không restart UI
- **📱 Responsive Design:** Hoạt động tốt trên mobile và desktop

---

## 🏗️ Architecture

### Backend API (`backend_api.py`)
- **Framework:** Flask với CORS support
- **Model:** Tự động ưu tiên load Vintern-1B-v3.5 nếu có; fallback sang CLIP-based VLM Classifier nếu không tìm thấy Vintern
- **Model path mặc định:** ../Vintern-1B-v3_5 (có thể truyền model_path qua API để nạp model fine-tuned)
- **Device:** CUDA nếu khả dụng; dtype fp16/bfloat16 phù hợp

- **Endpoints:**
  - `GET /health` - Health check
  - `POST /load_model` - Load/reload model
  - `POST /predict` - Main prediction endpoint
  - `GET /categories` - Get available categories
  - `GET /model_info` - Model information

### Frontend UI (`frontend_ui.html`)
- **Technology:** Pure HTML/CSS/JavaScript
- **Features:** Drag & drop, image preview, real-time results
- **Design:** Modern gradient UI với responsive layout
- **API Integration:** RESTful calls to backend

---

## 🚀 Quick Start

### Step 1: Install Dependencies
```bash
cd UI_For_VLM
pip install -r requirements.txt
```

### Step 2: Start Backend API
```bash
python backend_api.py
```
Backend sẽ chạy trên: `http://localhost:5000`

### Step 3: Open Frontend UI
```bash
# Mở file trực tiếp trong browser
start frontend_ui.html  # Windows
# hoặc
open frontend_ui.html   # macOS
```

### Step 4: Test Application
1. **Check Status:** UI sẽ hiển thị backend status
2. **Upload Image:** Drag & drop hoặc click để chọn ảnh món ăn
3. **Enter Query:** Nhập câu hỏi (mặc định: "What Vietnamese food is this?")
4. **Analyze:** Click "Analyze Food" để nhận predictions
5. **View Results:** Xem top 5 predictions với confidence scores

---

## 🔧 Configuration

### Model Loading
```python
# Backend tự động load model khi start
# Có thể load custom model path:
POST /load_model
{
    "model_path": "path/to/fine-tuned-model.pth"
}
```

### Supported Image Formats
- **JPEG/JPG**
- **PNG** 
- **GIF**
- **BMP**
- **WebP**

### Query Examples
```
"What Vietnamese food is this?"
"Describe this dish"
"What ingredients are in this food?"
"Is this a traditional Vietnamese dish?"
"What category does this food belong to?"
```

---

## 📊 API Documentation

### Health Check
```http
GET /health
Response: {
    "status": "healthy",
    "model_loaded": true,
    "categories": 102,
    "device": "cuda"
}
```

### Prediction
```http
POST /predict
Content-Type: application/json

{
    "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "text": "What Vietnamese food is this?"
}

Response: {
    "success": true,
    "predictions": [
        {
            "category": "bánh mì",
            "confidence": 0.95,
            "percentage": "95.0%"
        },
        {
            "category": "bánh",
            "confidence": 0.03,
            "percentage": "3.0%"
        }
    ],
    "top_prediction": {
        "category": "bánh mì",
        "confidence": 0.95,
        "percentage": "95.0%"
    },
    "query": "What Vietnamese food is this?"
}
```

### Load Model
```http
POST /load_model
Content-Type: application/json

{
    "model_path": "optional/path/to/model.pth"
}

Response: {
    "success": true,
    "message": "Model loaded successfully",
    "categories": ["bánh mì", "trà sữa", "phở", ...]
}
```

---

## 🎨 UI Features

### Modern Design
- **Gradient Backgrounds:** Beautiful color schemes
- **Card-based Layout:** Clean, organized interface
- **Hover Effects:** Interactive elements
- **Loading Animations:** Smooth user experience

### Responsive Layout
- **Desktop:** Two-column layout
- **Mobile:** Single-column stacked layout
- **Tablet:** Adaptive grid system

### Interactive Elements
- **Drag & Drop:** Easy image upload
- **Real-time Preview:** Instant image preview
- **Progress Indicators:** Loading spinners
- **Status Monitoring:** Backend connection status

---

## 🔄 Model Integration

### Flexible Model Loading
```python
# Backend có thể load different models:
# 1. Base CLIP model (default)
# 2. Fine-tuned weights từ progression_finetune/
# 3. Custom model paths
# 4. Different architectures (với code modifications)
```

### Model Switching
```javascript
// Frontend có thể trigger model reload:
async function loadModel() {
    const response = await fetch('/load_model', {
        method: 'POST',
        body: JSON.stringify({
            model_path: 'new/model/path.pth'
        })
    });
}
```

---

## 🚨 Troubleshooting

### Common Issues

**1. Backend Not Starting:**
```bash
# Check dependencies
pip install -r requirements.txt

# Check port availability
netstat -an | grep 5000

# Run with debug
python backend_api.py
```

**2. CORS Errors:**
```javascript
// Backend includes CORS headers
// If still issues, check browser console
// Try different browser or disable security temporarily
```

**3. Model Loading Errors:**
```python
# Check model path exists
# Verify model architecture matches
# Check CUDA/CPU compatibility
# Review backend logs for details
```

**4. Image Upload Issues:**
```javascript
// Check image size (recommend < 10MB)
// Verify image format supported
// Check base64 encoding
// Review browser console for errors
```

### Performance Optimization

**Backend:**
- Use GPU if available
- Batch processing for multiple images
- Model caching
- Image preprocessing optimization

**Frontend:**
- Image compression before upload
- Lazy loading for large images
- Debounced API calls
- Local caching of results

---

## 📈 Usage Examples

### Basic Food Recognition
1. Upload ảnh bánh mì
2. Query: "What Vietnamese food is this?"
3. Result: "bánh mì" với 95% confidence

### Detailed Description
1. Upload ảnh phở
2. Query: "Describe this dish in detail"
3. Result: Detailed description của phở

### Ingredient Analysis
1. Upload ảnh cơm
2. Query: "What ingredients are in this food?"
3. Result: List of ingredients detected

---

## 🔮 Future Enhancements

### Planned Features
- **🎥 Video Upload:** Analyze food in videos
- **📱 Mobile App:** Native mobile application
- **🗣️ Voice Input:** Voice queries support
- **🌐 Multi-language:** Support multiple languages
- **📊 Analytics:** Usage statistics and insights
- **🔐 Authentication:** User accounts and history
- **💾 Database:** Store predictions and feedback
- **🤖 Chatbot:** Conversational food assistant

### Technical Improvements
- **⚡ Performance:** Faster inference times
- **🎯 Accuracy:** Better model architectures
- **📦 Deployment:** Docker containerization
- **☁️ Cloud:** Cloud deployment options
- **🔄 CI/CD:** Automated testing and deployment

---

## 📄 File Structure

```
vlm_ui_app/
├── backend_api.py          # Flask backend API
├── frontend_ui.html        # Web interface
├── requirements.txt        # Python dependencies
├── README.md              # This documentation
└── static/                # Static assets (optional)
    ├── css/
    ├── js/
    └── images/
```

---

## 🎯 Success Metrics

### User Experience
- **⚡ Fast Response:** < 3 seconds prediction time
- **🎯 High Accuracy:** > 90% correct predictions
- **📱 Cross-platform:** Works on all devices
- **🔄 Reliability:** 99% uptime

### Technical Performance
- **💾 Memory Usage:** < 2GB RAM
- **🔥 GPU Utilization:** Efficient CUDA usage
- **📊 Throughput:** > 10 predictions/minute
- **🔧 Maintainability:** Easy model updates

---

**🎯 Mission: Provide intuitive interface for Vietnamese Food VLM testing**

**🚀 Status: Ready for deployment and testing**

*Generated: July 31, 2025 - VLM UI Application*
