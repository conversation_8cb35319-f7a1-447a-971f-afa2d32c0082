#!/usr/bin/env python3
"""
Approach 1: Text-only finetune
Finetune chỉ phần Language Model, freeze Vision Model
"""

import os
import torch
import torch.nn as nn
import pandas as pd
import logging
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from tqdm import tqdm

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TextOnlyDataset(Dataset):
    def __init__(self, df, tokenizer, max_length=128):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.df)
    
    def __getitem__(self, idx):
        food_name = self.df.iloc[idx]['food_name']
        
        # Tạo text training format
        text = f"Món ăn Việt Nam: {food_name}"
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze()
        }

def freeze_vision_components(model):
    """Freeze vision model và chỉ train language model"""
    logger.info("Freezing vision components...")
    
    # Freeze vision model
    if hasattr(model, 'vision_model'):
        for param in model.vision_model.parameters():
            param.requires_grad = False
        logger.info("Vision model frozen")
    
    # Freeze MLP connector (optional - có thể train để adapt)
    if hasattr(model, 'mlp1'):
        for param in model.mlp1.parameters():
            param.requires_grad = False
        logger.info("MLP connector frozen")
    
    # Keep language model trainable
    if hasattr(model, 'language_model'):
        for param in model.language_model.parameters():
            param.requires_grad = True
        logger.info("Language model kept trainable")
    
    # Count trainable parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    logger.info(f"Trainable ratio: {trainable_params/total_params*100:.2f}%")
    
    return model

def create_dummy_inputs_for_vlm(input_ids, attention_mask, device):
    """Tạo dummy inputs cho VLM để có thể forward pass"""
    batch_size = input_ids.shape[0]
    seq_len = input_ids.shape[1]
    
    # Dummy pixel values (không sử dụng thực tế)
    dummy_pixel_values = torch.zeros(batch_size, 3, 448, 448, dtype=torch.float16).to(device)
    
    # Dummy image flags (all zeros = no image)
    dummy_image_flags = torch.zeros(batch_size, seq_len, dtype=torch.long).to(device)
    
    return dummy_pixel_values, dummy_image_flags

def train_text_only(model, train_dataloader, device, num_epochs=1, learning_rate=5e-5):
    """Training loop cho text-only finetune"""
    
    # Setup optimizer chỉ cho trainable parameters
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.AdamW(trainable_params, lr=learning_rate)
    
    model.train()
    total_loss = 0
    step = 0
    max_steps = 50  # Limit for testing
    
    logger.info(f"Starting training for {num_epochs} epoch(s), max {max_steps} steps...")
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1}"):
            if step >= max_steps:
                break
            
            # Move to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            # Create dummy inputs for VLM
            dummy_pixel_values, dummy_image_flags = create_dummy_inputs_for_vlm(
                input_ids, attention_mask, device
            )
            
            try:
                # Forward pass
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    pixel_values=dummy_pixel_values,
                    image_flags=dummy_image_flags,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                epoch_loss += loss.item()
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                step += 1
                
                if step % 10 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps}, Loss: {loss.item():.4f}, Avg Loss: {avg_loss:.4f}")
                
            except Exception as e:
                logger.error(f"Error in training step {step}: {str(e)}")
                continue
        
        if step >= max_steps:
            break
        
        avg_epoch_loss = epoch_loss / len(train_dataloader)
        logger.info(f"Epoch {epoch+1} completed. Average loss: {avg_epoch_loss:.4f}")
    
    return model

def test_generation(model, tokenizer, device):
    """Test generation sau khi finetune"""
    logger.info("Testing generation...")
    
    model.eval()
    test_prompts = [
        "Món ăn Việt Nam:",
        "Món ăn Việt Nam: cà phê",
        "Món ăn Việt Nam: bánh mì"
    ]
    
    for prompt in test_prompts:
        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(device)
            
            # Create dummy inputs
            dummy_pixel_values, dummy_image_flags = create_dummy_inputs_for_vlm(
                inputs['input_ids'], inputs['attention_mask'], device
            )
            
            with torch.no_grad():
                outputs = model.generate(
                    input_ids=inputs['input_ids'],
                    attention_mask=inputs['attention_mask'],
                    pixel_values=dummy_pixel_values,
                    image_flags=dummy_image_flags,
                    max_length=inputs['input_ids'].shape[1] + 20,
                    num_return_sequences=1,
                    temperature=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(prompt):].strip()
            logger.info(f"Prompt: '{prompt}' -> Generated: '{generated}'")
            
        except Exception as e:
            logger.warning(f"Generation failed for '{prompt}': {str(e)}")

def main():
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach1_output"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    logger.info("Loading model...")
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    logger.info(f"Using device: {device}")
    
    # Freeze vision components
    model = freeze_vision_components(model)
    
    # Load data
    logger.info("Loading training data...")
    df = pd.read_csv(train_data_path)
    df_small = df.head(200).copy()  # Small subset for testing
    df_small = df_small.dropna(subset=['food_name'])
    
    # Create dataset
    train_dataset = TextOnlyDataset(df_small, tokenizer)
    train_dataloader = DataLoader(train_dataset, batch_size=2, shuffle=True)
    
    logger.info(f"Training on {len(df_small)} samples")
    
    # Train
    model = train_text_only(model, train_dataloader, device, num_epochs=1)
    
    # Save model
    logger.info("Saving model...")
    model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test generation
    test_generation(model, tokenizer, device)
    
    logger.info("Approach 1 (Text-only finetune) completed!")

if __name__ == "__main__":
    main()
