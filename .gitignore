# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.bin

# Large data files
*.csv
*.json
*.parquet
*.h5
*.hdf5
*.pkl
*.pickle

# Model files (use Git LFS for these)
*.safetensors
*.model

# Logs
*.log
logs/
wandb/

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data directories (too large for git)
Original_Data/
Preprocessing_Data/All_Data_After_Preprocessing/
data/raw/
data/processed/

# Temporary files
tmp/
temp/
*.tmp

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
