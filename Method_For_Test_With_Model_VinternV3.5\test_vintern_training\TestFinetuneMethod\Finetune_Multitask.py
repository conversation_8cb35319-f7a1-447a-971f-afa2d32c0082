#!/usr/bin/env python3
"""
APPROACH 4: Multi-Task Learning
Train model đồng thời nhiều task để học representation tốt hơn
"""

import os
import torch
import pandas as pd
import logging
import random
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
from tqdm import tqdm
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiTaskDataset(Dataset):
    """
    Dataset cho Multi-Task Learning
    
    3 Tasks:
    1. Food Recognition: Tên món ăn
    2. Food Classification: Loại món (đồ uống/món ăn/tráng miệng)
    3. Food Description: Mô tả món ăn
    """
    def __init__(self, df, tokenizer, max_length=150):
        self.df = df
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.multi_tasks = self.create_multi_tasks()
        
        logger.info(f"Tạo MultiTaskDataset với {len(self.multi_tasks)} multi-task examples")
    
    def classify_food_type(self, food_name):
        """Phân loại món ăn dựa trên tên"""
        food_lower = food_name.lower()
        
        # Đồ uống
        drink_keywords = ['cà phê', 'trà', 'nước', 'sinh tố', 'sữa', 'juice', 'pepsi', 'coca', 'bia', 'rượu']
        if any(keyword in food_lower for keyword in drink_keywords):
            return "đồ uống"
        
        # Tráng miệng
        dessert_keywords = ['bánh', 'kem', 'chè', 'pudding', 'tiramisu', 'mousse', 'flan']
        if any(keyword in food_lower for keyword in dessert_keywords):
            return "tráng miệng"
        
        # Món ăn chính
        return "món ăn"
    
    def generate_description(self, food_name, food_type):
        """Tạo mô tả cho món ăn"""
        descriptions = {
            "đồ uống": [
                f"{food_name} là một loại đồ uống thơm ngon",
                f"Đây là {food_name}, một thức uống phổ biến",
                f"{food_name} - thức uống được yêu thích"
            ],
            "món ăn": [
                f"{food_name} là một món ăn truyền thống",
                f"Đây là {food_name}, món ăn đặc trưng",
                f"{food_name} - món ăn ngon và bổ dưỡng"
            ],
            "tráng miệng": [
                f"{food_name} là một món tráng miệng ngọt ngào",
                f"Đây là {food_name}, món tráng miệng hấp dẫn",
                f"{food_name} - món tráng miệng được ưa chuộng"
            ]
        }
        
        return random.choice(descriptions[food_type])
    
    def create_multi_tasks(self):
        """Tạo multi-task examples"""
        tasks = []
        
        for _, row in self.df.iterrows():
            food_name = row['food_name'].strip().lower()
            
            if len(food_name) < 3:
                continue
            
            food_type = self.classify_food_type(food_name)
            description = self.generate_description(food_name, food_type)
            
            # Task 1: Food Recognition
            tasks.append({
                'task_type': 'recognition',
                'instruction': 'Nhận diện tên món ăn:',
                'response': food_name,
                'full_text': f"### Task: Food Recognition\n### Instruction: Nhận diện tên món ăn:\n### Response: {food_name}"
            })
            
            # Task 2: Food Classification  
            tasks.append({
                'task_type': 'classification',
                'instruction': f'Phân loại món ăn: {food_name}',
                'response': food_type,
                'full_text': f"### Task: Food Classification\n### Instruction: Phân loại món ăn: {food_name}\n### Response: {food_type}"
            })
            
            # Task 3: Food Description
            tasks.append({
                'task_type': 'description',
                'instruction': f'Mô tả món ăn: {food_name}',
                'response': description,
                'full_text': f"### Task: Food Description\n### Instruction: Mô tả món ăn: {food_name}\n### Response: {description}"
            })
        
        # Shuffle để mix các tasks
        random.shuffle(tasks)
        return tasks
    
    def __len__(self):
        return len(self.multi_tasks)
    
    def __getitem__(self, idx):
        item = self.multi_tasks[idx]
        text = item['full_text']
        
        encoding = self.tokenizer(
            text,
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            'input_ids': encoding['input_ids'].squeeze(),
            'attention_mask': encoding['attention_mask'].squeeze(),
            'labels': encoding['input_ids'].squeeze(),
            'task_type': item['task_type']
        }

def setup_multitask_lora(model):
    """Setup LoRA cho Multi-Task Learning"""
    logger.info("=== THIẾT LẬP MULTI-TASK LORA ===")
    
    for param in model.parameters():
        param.requires_grad = False
    
    if hasattr(model, 'language_model'):
        llm = model.language_model
        
        # LoRA config cho multi-task (capacity cao hơn)
        lora_config = LoraConfig(
            r=48,                    # Rank cao cho multi-task
            lora_alpha=96,           # Alpha cao
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            lora_dropout=0.03,       # Dropout thấp
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )
        
        model.language_model = get_peft_model(llm, lora_config)
        logger.info("✅ Multi-Task LoRA applied")
        model.language_model.print_trainable_parameters()
    
    return model

def train_multitask_model(model, train_dataloader, device, num_epochs=1):
    """Training Multi-Task Model"""
    logger.info("=== BẮT ĐẦU MULTI-TASK TRAINING ===")
    
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    optimizer = torch.optim.AdamW(trainable_params, lr=3e-4)  # Learning rate cao hơn
    
    model.train()
    training_history = []
    total_loss = 0
    step = 0
    max_steps = 80  # Nhiều steps hơn cho multi-task
    
    task_losses = {'recognition': [], 'classification': [], 'description': []}
    
    for epoch in range(num_epochs):
        logger.info(f"--- MULTI-TASK EPOCH {epoch + 1}/{num_epochs} ---")
        
        for batch in tqdm(train_dataloader, desc=f"Multi-Task Training"):
            if step >= max_steps:
                break
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            task_types = batch['task_type']
            
            try:
                llm = model.language_model
                outputs = llm(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                total_loss += loss.item()
                
                # Track loss per task type
                for i, task_type in enumerate(task_types):
                    task_losses[task_type].append(loss.item())
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                step += 1
                
                if step % 15 == 0:
                    avg_loss = total_loss / step
                    logger.info(f"Step {step}/{max_steps} - Loss: {loss.item():.4f} - Avg Loss: {avg_loss:.4f}")
                    
                    # Log task-specific losses
                    for task_type, losses in task_losses.items():
                        if losses:
                            avg_task_loss = sum(losses[-10:]) / len(losses[-10:])  # Last 10
                            logger.info(f"  {task_type}: {avg_task_loss:.4f}")
                
                training_history.append({
                    'step': step,
                    'loss': loss.item(),
                    'avg_loss': total_loss / step
                })
                
            except Exception as e:
                logger.error(f"Multi-task training error: {str(e)}")
                step += 1
                continue
        
        if step >= max_steps:
            break
    
    logger.info("✅ Multi-Task training completed!")
    return model, training_history

def test_multitask_model(model, tokenizer, device):
    """Test Multi-Task Model"""
    logger.info("=== TESTING MULTI-TASK MODEL ===")
    
    model.eval()
    llm = model.language_model
    
    # Test các task khác nhau
    test_cases = [
        # Task 1: Recognition
        "### Task: Food Recognition\n### Instruction: Nhận diện tên món ăn:\n### Response:",
        
        # Task 2: Classification
        "### Task: Food Classification\n### Instruction: Phân loại món ăn: cà phê sữa\n### Response:",
        "### Task: Food Classification\n### Instruction: Phân loại món ăn: bánh flan\n### Response:",
        
        # Task 3: Description
        "### Task: Food Description\n### Instruction: Mô tả món ăn: phở bò\n### Response:",
        "### Task: Food Description\n### Instruction: Mô tả món ăn: trà sữa\n### Response:",
    ]
    
    test_results = []
    
    for test_case in test_cases:
        try:
            inputs = tokenizer(test_case, return_tensors="pt").to(device)
            
            with torch.no_grad():
                outputs = llm.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + 25,
                    temperature=0.6,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            generated = response[len(test_case):].strip()
            
            if generated:
                generated = generated.split('\n')[0].strip()
            
            task_type = test_case.split('### Task: ')[1].split('\n')[0]
            
            test_results.append({
                'task_type': task_type,
                'test_case': test_case.split('### Response:')[0].strip(),
                'generated': generated
            })
            
            logger.info(f"{task_type}: '{generated}'")
            
        except Exception as e:
            logger.warning(f"Generation failed: {str(e)}")
    
    return test_results

def main():
    logger.info("🚀 BẮT ĐẦU APPROACH 4: MULTI-TASK LEARNING")
    
    # Configuration
    model_path = "./Vintern-1B-v3_5"
    train_data_path = "./data_splits/train.csv"
    output_dir = "./approach4_multitask_model"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        low_cpu_mem_usage=True
    )
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # Setup Multi-Task LoRA
    model = setup_multitask_lora(model)
    
    # Load data
    train_df = pd.read_csv(train_data_path)
    train_df = train_df.head(200).dropna(subset=['food_name'])  # Smaller for multi-task
    
    # Create multi-task dataset
    train_dataset = MultiTaskDataset(train_df, tokenizer)
    train_dataloader = DataLoader(train_dataset, batch_size=3, shuffle=True)
    
    logger.info(f"Multi-task examples: {len(train_dataset)}")
    
    # Training
    model, history = train_multitask_model(model, train_dataloader, device)
    
    # Save model
    model.language_model.save_pretrained(output_dir)
    tokenizer.save_pretrained(output_dir)
    
    # Test model
    test_results = test_multitask_model(model, tokenizer, device)
    
    # Save results
    results = {
        'method': 'Multi-Task Learning',
        'training_history': history,
        'test_results': test_results,
        'model_path': output_dir,
        'dataset_size': len(train_df),
        'multitask_examples': len(train_dataset)
    }
    
    with open('output_multitask.txt', 'w', encoding='utf-8') as f:
        f.write("=== APPROACH 4: MULTI-TASK LEARNING RESULTS ===\n\n")
        f.write(f"Method: {results['method']}\n")
        f.write(f"Dataset size: {results['dataset_size']} samples\n")
        f.write(f"Multi-task examples: {results['multitask_examples']}\n")
        f.write(f"Model saved to: {results['model_path']}\n\n")
        
        f.write("Training History (last 10 steps):\n")
        for entry in history[-10:]:
            f.write(f"Step {entry['step']}: Loss={entry['loss']:.4f}, Avg Loss={entry['avg_loss']:.4f}\n")
        
        f.write("\nMulti-Task Test Results:\n")
        for result in test_results:
            f.write(f"{result['task_type']}: '{result['generated']}'\n")
    
    logger.info("✅ APPROACH 4 HOÀN THÀNH!")
    logger.info("📄 Kết quả đã lưu vào output_multitask.txt")

if __name__ == "__main__":
    main()
