#!/usr/bin/env python3
"""
Final 32k Vietnamese Food Dataset Sampler
- Guaranteed to produce exactly 32,000 samples
- Optimized for FinetuneWithProgressiveMethod.py compatibility
- Balanced sampling with quality selection
"""

import pandas as pd
import numpy as np
import logging
import sys
from pathlib import Path
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_32k_sampling.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class Final32kSampler:
    """Final sampler to produce exactly 32k samples"""
    
    def __init__(self, input_file="Final_Ultimate_Complete_Dataset.csv", 
                 output_file="Final_32k_Dataset.csv"):
        self.input_file = input_file
        self.output_file = output_file
        self.target_samples = 32000
        
        logger.info(f"Final 32k Sampler: {input_file} -> {output_file}")
    
    def load_and_clean_data(self):
        """Load and clean the dataset"""
        logger.info("Loading and cleaning dataset...")
        
        df = pd.read_csv(self.input_file, encoding='utf-8')
        logger.info(f"Original dataset: {len(df):,} samples")
        
        # Clean data
        df = df.dropna(subset=['food_name', 'final_ultimate_category'])
        df = df[df['URL'].notna()]
        df = df[df['URL'].str.contains('http', case=False, na=False)]
        
        # Add normalized_name if missing
        if 'normalized_name' not in df.columns:
            df['normalized_name'] = df['food_name'].str.lower().str.strip()
        
        logger.info(f"Cleaned dataset: {len(df):,} samples, {df['final_ultimate_category'].nunique():,} categories")
        return df
    
    def calculate_exact_sampling_plan(self, df):
        """Calculate sampling plan to get exactly 32k samples"""
        categories = df['final_ultimate_category'].unique()
        category_counts = df['final_ultimate_category'].value_counts()
        
        total_categories = len(categories)
        base_per_category = self.target_samples // total_categories
        remainder = self.target_samples % total_categories
        
        logger.info(f"Base samples per category: {base_per_category}")
        logger.info(f"Extra samples to distribute: {remainder}")
        
        # Create sampling plan
        sampling_plan = {}
        
        # Give base amount to all categories
        for category in categories:
            available = category_counts[category]
            sampling_plan[category] = min(available, base_per_category)
        
        # Distribute remainder to largest categories
        if remainder > 0:
            largest_categories = category_counts.head(remainder).index
            for category in largest_categories:
                available = category_counts[category]
                current = sampling_plan[category]
                if current < available:
                    sampling_plan[category] += 1
        
        total_planned = sum(sampling_plan.values())
        logger.info(f"Planned total: {total_planned:,} samples")
        
        # If still short, add more samples to categories with availability
        while total_planned < self.target_samples:
            added = False
            for category in category_counts.index:
                if total_planned >= self.target_samples:
                    break
                available = category_counts[category]
                current = sampling_plan[category]
                if current < available:
                    sampling_plan[category] += 1
                    total_planned += 1
                    added = True
            
            if not added:  # No more samples available
                break
        
        final_total = sum(sampling_plan.values())
        logger.info(f"Final sampling plan: {final_total:,} samples")
        
        return sampling_plan
    
    def select_best_samples(self, samples_df, count):
        """Select best samples from a group"""
        if len(samples_df) <= count:
            return samples_df.to_dict('records')
        
        # Calculate quality scores
        samples_df = samples_df.copy()
        samples_df['quality_score'] = 0
        
        # URL quality (5 points)
        valid_urls = (samples_df['URL'].str.len() > 30) & \
                    (samples_df['URL'].str.contains('https', case=False, na=False))
        samples_df.loc[valid_urls, 'quality_score'] += 5
        
        # Food name quality (3 points)
        name_lengths = samples_df['food_name'].str.len()
        good_names = (name_lengths >= 3) & (name_lengths <= 50)
        samples_df.loc[good_names, 'quality_score'] += 3
        
        # Avoid generic terms (2 points)
        generic_terms = ['món', 'thức ăn', 'đồ ăn', 'food', 'dish']
        non_generic = ~samples_df['food_name'].str.lower().isin(generic_terms)
        samples_df.loc[non_generic, 'quality_score'] += 2
        
        # Prefer samples with classification method (1 point)
        if 'classification_method' in samples_df.columns:
            has_method = samples_df['classification_method'].notna()
            samples_df.loc[has_method, 'quality_score'] += 1
        
        # Sort by quality score and select top samples
        best_samples = samples_df.nlargest(count, 'quality_score')
        
        return best_samples.to_dict('records')
    
    def execute_final_sampling(self, df, sampling_plan):
        """Execute the final sampling plan"""
        logger.info("Executing final sampling...")
        start_time = time.time()
        
        all_samples = []
        processed = 0
        
        for category, target_count in sampling_plan.items():
            category_df = df[df['final_ultimate_category'] == category]
            
            # Get unique food names in this category
            unique_foods = category_df['food_name'].unique()
            
            if len(unique_foods) >= target_count:
                # Enough unique foods: select one sample per food
                selected_foods = np.random.choice(unique_foods, target_count, replace=False)
                for food in selected_foods:
                    food_samples = category_df[category_df['food_name'] == food]
                    best_samples = self.select_best_samples(food_samples, 1)
                    all_samples.extend(best_samples)
            else:
                # Not enough unique foods: select multiple samples per food
                samples_per_food = target_count // len(unique_foods)
                remainder = target_count % len(unique_foods)
                
                for i, food in enumerate(unique_foods):
                    food_samples = category_df[category_df['food_name'] == food]
                    count = samples_per_food + (1 if i < remainder else 0)
                    best_samples = self.select_best_samples(food_samples, count)
                    all_samples.extend(best_samples)
            
            processed += 1
            if processed % 200 == 0:
                progress = (processed / len(sampling_plan)) * 100
                logger.info(f"Progress: {progress:.1f}% ({processed}/{len(sampling_plan)})")
        
        # Create final dataframe
        result_df = pd.DataFrame(all_samples)
        
        elapsed = time.time() - start_time
        logger.info(f"Sampling completed in {elapsed/60:.1f} minutes")
        logger.info(f"Final result: {len(result_df):,} samples")
        
        return result_df
    
    def save_final_results(self, df):
        """Save final results"""
        logger.info("Saving final results...")
        
        # Save dataset
        df.to_csv(self.output_file, index=False, encoding='utf-8')
        logger.info(f"Final dataset saved: {self.output_file}")
        
        # Generate comprehensive report
        report_file = self.output_file.replace('.csv', '_final_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("FINAL 32K VIETNAMESE FOOD DATASET REPORT\n")
            f.write("="*60 + "\n\n")
            f.write(f"Target samples: {self.target_samples:,}\n")
            f.write(f"Actual samples: {len(df):,}\n")
            f.write(f"Achievement rate: {len(df)/self.target_samples*100:.1f}%\n")
            f.write(f"Total categories: {df['final_ultimate_category'].nunique():,}\n")
            f.write(f"Unique food names: {df['food_name'].nunique():,}\n")
            f.write(f"Valid URLs: {df['URL'].notna().sum():,}\n")
            
            # Category statistics
            category_counts = df['final_ultimate_category'].value_counts()
            f.write(f"\nCategory Distribution:\n")
            f.write(f"Min samples per category: {category_counts.min()}\n")
            f.write(f"Max samples per category: {category_counts.max()}\n")
            f.write(f"Mean samples per category: {category_counts.mean():.1f}\n")
            f.write(f"Median samples per category: {category_counts.median():.1f}\n")
            
            # Compatibility check
            f.write(f"\nFinetuneWithProgressiveMethod Compatibility:\n")
            required_cols = ['food_name', 'final_ultimate_category', 'URL', 'normalized_name']
            for col in required_cols:
                status = "✓" if col in df.columns else "✗"
                f.write(f"{status} {col}\n")
            
            f.write(f"\nDataset is ready for Vintern V3.5 fine-tuning!\n")
        
        logger.info(f"Final report saved: {report_file}")
        
        # File size
        file_size = Path(self.output_file).stat().st_size / (1024*1024)
        logger.info(f"Output file size: {file_size:.1f} MB")
    
    def run(self):
        """Main execution"""
        logger.info("Starting Final 32k Sampling Process")
        
        try:
            # Load and clean data
            df = self.load_and_clean_data()
            
            # Calculate exact sampling plan
            sampling_plan = self.calculate_exact_sampling_plan(df)
            
            # Execute sampling
            result_df = self.execute_final_sampling(df, sampling_plan)
            
            # Save results
            self.save_final_results(result_df)
            
            logger.info("Final 32k sampling completed successfully!")
            return result_df
            
        except Exception as e:
            logger.error(f"Final sampling failed: {e}")
            raise

def main():
    """Main function"""
    print("Final 32k Vietnamese Food Dataset Sampler")
    print("="*60)
    
    sampler = Final32kSampler()
    
    try:
        result_df = sampler.run()
        
        print(f"\n🎉 FINAL SAMPLING COMPLETED!")
        print(f"📊 Samples: {len(result_df):,}")
        print(f"🎯 Target: {32000:,}")
        print(f"✅ Success rate: {len(result_df)/32000*100:.1f}%")
        print(f"📁 Output: Final_32k_Dataset.csv")
        print(f"📋 Report: Final_32k_Dataset_final_report.txt")
        print("🚀 Ready for Vintern V3.5 fine-tuning!")
        
    except Exception as e:
        print(f"❌ Final sampling failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
