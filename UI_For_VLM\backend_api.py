#!/usr/bin/env python3
"""
Backend API cho Vietnamese Food VLM
Kết nối với fine-tuned model và cung cấp API endpoints
"""

from flask import Flask, request, jsonify
import torch
import torch.nn as nn
from transformers import CLIPProcessor, CLIPModel
from PIL import Image
import io
import base64
import json
import logging
import os
from pathlib import Path
import pandas as pd

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Simple CORS handling
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

class VLMBackend:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.processor = None
        self.categories = []
        self.category_to_id = {}
        self.id_to_category = {}
        
        logger.info(f"🚀 VLM Backend initialized on device: {self.device}")
    
    def load_model(self, model_path=None):
        """Load Vintern-1B-v3.5 model"""
        try:
            logger.info("🔧 Loading Vintern-1B-v3.5 model...")

            # Try to load Vintern first
            vintern_path = "../Vintern-1B-v3_5"
            if os.path.exists(vintern_path):
                from transformers import AutoModel, AutoTokenizer

                self.model = AutoModel.from_pretrained(
                    vintern_path,
                    trust_remote_code=True,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                )

                self.tokenizer = AutoTokenizer.from_pretrained(
                    vintern_path,
                    trust_remote_code=True
                )

                self.model.to(self.device)
                self.model_type = "vintern"

                logger.info(f"✅ Vintern-1B-v3.5 loaded successfully!")
                return True

            # Fallback to CLIP if Vintern not available
            logger.info("Vintern not found, falling back to CLIP...")
            from transformers import CLIPProcessor, CLIPModel

            self.processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
            base_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")

            # Load categories from training data
            try:
                data_path = "../image_integration/vlm_training_dataset/training_pairs.csv"
                if os.path.exists(data_path):
                    df = pd.read_csv(data_path)
                    self.categories = sorted(df['category'].unique())
                else:
                    # Fallback categories
                    self.categories = [
                        "bánh mì", "trà sữa", "phở", "cơm", "bánh",
                        "americano", "brownie", "bánh chưng", "bánh cuốn"
                    ]
            except Exception as e:
                logger.warning(f"Could not load categories: {e}")
                self.categories = ["bánh mì", "trà sữa", "phở", "cơm"]

            if hasattr(self, 'model_type') and self.model_type == "vintern":
                # Vintern doesn't need category mappings
                logger.info(f"📊 Vintern model ready with {len(self.categories)} known categories")
                return True

            # CLIP needs category mappings
            self.category_to_id = {cat: idx for idx, cat in enumerate(self.categories)}
            self.id_to_category = {idx: cat for cat, idx in self.category_to_id.items()}
            
            # Create VLM classifier
            class VLMClassifier(nn.Module):
                def __init__(self, base_model, num_classes):
                    super().__init__()
                    self.base_model = base_model
                    self.dropout = nn.Dropout(0.3)
                    self.classifier = nn.Linear(base_model.config.projection_dim, num_classes)
                
                def forward(self, input_ids=None, pixel_values=None, attention_mask=None, **kwargs):
                    outputs = self.base_model(
                        input_ids=input_ids,
                        pixel_values=pixel_values,
                        attention_mask=attention_mask
                    )
                    
                    # Combine embeddings
                    image_embeds = outputs.image_embeds
                    text_embeds = outputs.text_embeds
                    combined = (image_embeds + text_embeds) / 2
                    combined = self.dropout(combined)
                    logits = self.classifier(combined)
                    
                    return {'logits': logits}
            
            self.model = VLMClassifier(base_model, len(self.categories))
            
            # Try to load fine-tuned weights if available
            if model_path and os.path.exists(model_path):
                logger.info(f"Loading fine-tuned weights from {model_path}")
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            else:
                logger.info("Using base model (no fine-tuned weights found)")
            
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"✅ Model loaded successfully!")
            logger.info(f"📊 Categories: {len(self.categories)}")
            logger.info(f"📊 Sample categories: {self.categories[:5]}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Model loading failed: {e}")
            return False
    
    def predict(self, image, text_query="What Vietnamese food is this?"):
        """Predict food category from image and text"""
        try:
            if self.model is None:
                return {"error": "Model not loaded"}

            # Check model type
            if hasattr(self, 'model_type') and self.model_type == "vintern":
                # Use Vintern model
                with torch.no_grad():
                    response = self.model.chat(
                        self.tokenizer,
                        pixel_values=image,
                        question=text_query,
                        generation_config=dict(max_new_tokens=100, do_sample=False)
                    )

                # Parse Vintern response to extract food categories
                predictions = self.parse_vintern_response(response)

                return {
                    "success": True,
                    "predictions": predictions,
                    "top_prediction": predictions[0] if predictions else None,
                    "query": text_query,
                    "raw_response": response,
                    "model_type": "Vintern-1B-v3.5"
                }

            else:
                # Use CLIP model
                inputs = self.processor(
                    text=[text_query],
                    images=[image],
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=77
                )

                # Move to device
                for key in inputs:
                    if isinstance(inputs[key], torch.Tensor):
                        inputs[key] = inputs[key].to(self.device)

                # Predict
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    logits = outputs['logits']
                    probabilities = torch.softmax(logits, dim=1)

                    # Get top predictions
                    top_probs, top_indices = torch.topk(probabilities, k=min(5, len(self.categories)))

                    predictions = []
                    for prob, idx in zip(top_probs[0], top_indices[0]):
                        category = self.id_to_category[idx.item()]
                        confidence = prob.item()
                        predictions.append({
                            "category": category,
                            "confidence": confidence,
                            "percentage": f"{confidence * 100:.1f}%"
                        })

                    return {
                        "success": True,
                        "predictions": predictions,
                        "top_prediction": predictions[0] if predictions else None,
                        "query": text_query,
                        "model_type": "CLIP-based"
                    }

        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {"error": str(e)}

    def parse_vintern_response(self, response):
        """Parse Vintern response to extract food predictions"""
        try:
            predictions = []

            # Simple parsing - look for Vietnamese food names in response
            response_lower = response.lower()

            # Check against known categories
            for category in self.categories:
                if category.lower() in response_lower:
                    # Simple confidence based on position in response
                    position = response_lower.find(category.lower())
                    confidence = max(0.1, 1.0 - (position / len(response_lower)))

                    predictions.append({
                        "category": category,
                        "confidence": confidence,
                        "percentage": f"{confidence * 100:.1f}%"
                    })

            # Sort by confidence
            predictions.sort(key=lambda x: x['confidence'], reverse=True)

            # If no matches found, create a general prediction
            if not predictions:
                predictions.append({
                    "category": "Vietnamese food",
                    "confidence": 0.8,
                    "percentage": "80.0%"
                })

            return predictions[:5]  # Return top 5

        except Exception as e:
            logger.error(f"Error parsing Vintern response: {e}")
            return [{
                "category": "Unknown food",
                "confidence": 0.5,
                "percentage": "50.0%"
            }]

# Initialize backend
vlm_backend = VLMBackend()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "model_loaded": vlm_backend.model is not None,
        "categories": len(vlm_backend.categories),
        "device": str(vlm_backend.device)
    })

@app.route('/load_model', methods=['POST'])
def load_model():
    """Load or reload model"""
    data = request.get_json()
    model_path = data.get('model_path') if data else None
    
    success = vlm_backend.load_model(model_path)
    
    return jsonify({
        "success": success,
        "message": "Model loaded successfully" if success else "Model loading failed",
        "categories": vlm_backend.categories[:10]  # Show first 10 categories
    })

@app.route('/predict', methods=['POST'])
def predict():
    """Main prediction endpoint"""
    try:
        data = request.get_json()
        
        # Get image from base64
        image_data = data.get('image')
        text_query = data.get('text', "What Vietnamese food is this?")
        
        if not image_data:
            return jsonify({"error": "No image provided"}), 400
        
        # Decode base64 image
        if image_data.startswith('data:image'):
            image_data = image_data.split(',')[1]
        
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes)).convert('RGB')
        
        # Predict
        result = vlm_backend.predict(image, text_query)
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"Prediction endpoint error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/categories', methods=['GET'])
def get_categories():
    """Get all available categories"""
    return jsonify({
        "categories": vlm_backend.categories,
        "total": len(vlm_backend.categories)
    })

@app.route('/model_info', methods=['GET'])
def model_info():
    """Get model information"""
    return jsonify({
        "model_loaded": vlm_backend.model is not None,
        "device": str(vlm_backend.device),
        "categories_count": len(vlm_backend.categories),
        "sample_categories": vlm_backend.categories[:10],
        "model_type": "CLIP-based VLM Classifier"
    })

if __name__ == '__main__':
    # Load model on startup
    logger.info("🚀 Starting VLM Backend API...")
    vlm_backend.load_model()
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)
