# 🚀 Multi-LoRA Finetune cho Food + Harmful Detection

**Multi-task VLM training với LoRA adapters rank 32+ cho Vietnamese Food Classification + Harmful Content Detection**

## 🎯 Mục tiêu

Train model Vintern VLM có thể thực hiện **2 tasks đồng thời**:
1. **Food Classification**: Nhận diện món ăn Việt Nam (1400+ categories)
2. **Harmful Detection**: Phát hiện nội dung có hại (safe/harmful binary)

## 🔧 Kiến trúc Multi-LoRA

### **Core Architecture:**
- **Shared Backbone**: Vintern-1B-v3_5 base model (frozen)
- **Task-specific LoRA adapters**: 
  - Food LoRA (rank 64) cho food classification
  - Harmful LoRA (rank 32) cho harmful detection
- **Specialized Classification Heads**: Multi-layer heads cho mỗi task
- **Dynamic Task Routing**: Tự động route samples đến appropriate LoRA

### **Lessons Learned từ Progressive Training:**
✅ **Stable finite epochs** (không infinite loop)  
✅ **Proper gradient clipping** (max_norm=1.0)  
✅ **Memory management** (cleanup sau mỗi batch)  
✅ **Enhanced error handling** (skip problematic batches)  
✅ **Multi-task loss balancing** (focal loss + task weights)  
✅ **OneCycleLR scheduler** (stable convergence)  
✅ **Separate LR cho LoRA vs heads** (2x LR cho heads)  

## 📊 Dataset Pipeline

### **Multi-Task Dataset:**
- **Food Dataset**: FINAL_ULTIMATE_COMPLETE_DATASET.csv (700k+ samples)
- **Harmful Dataset**: Synthetic + real harmful content datasets
- **Balanced Sampling**: 60% food, 40% harmful (configurable)
- **Task-aware Augmentation**: Different transforms cho mỗi task

### **Data Format:**
```python
{
    'image_tensor': torch.Tensor,     # [1, 3, 448, 448]
    'task_type': str,                 # 'food_classification' or 'harmful_detection'
    'task_id': int,                   # 0 for food, 1 for harmful
    'label': str,                     # Category name or 'safe'/'harmful'
    'instruction': str,               # Task-specific instruction
    'response': str,                  # Expected response
    'metadata': dict                  # Additional info
}
```

## 🚀 Quick Start

### **1. Setup Environment:**
```bash
pip install torch transformers peft accelerate
pip install pillow requests pandas numpy
pip install scikit-learn matplotlib seaborn
```

### **2. Prepare Data:**
```bash
# Food dataset (đã có)
food_data = "FINAL_ULTIMATE_COMPLETE_DATASET.csv"

# Harmful dataset (tạo synthetic nếu chưa có)
harmful_data = "harmful_dataset.csv"  # Sẽ tự tạo nếu không tồn tại
```

### **3. Analyze Dataset:**
```bash
python run_multi_lora_training.py --analyze_only \
  --food_data ../Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_DATASET.csv \
  --harmful_data harmful_dataset.csv
```

### **4. Training với Optimal Config:**
```bash
# Recommended configuration
python run_multi_lora_training.py \
  --food_data ../Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_DATASET.csv \
  --harmful_data harmful_dataset.csv \
  --food_rank 64 \
  --harmful_rank 32 \
  --learning_rate 2e-4 \
  --batch_size 8 \
  --epochs 12 \
  --experiment_name "optimal_config"
```

### **5. Hyperparameter Optimization:**
```bash
python optimization_and_testing.py
```

## 📈 Expected Performance

### **Target Metrics:**
- **Food Classification Accuracy**: >85% (1400+ categories)
- **Harmful Detection Accuracy**: >95% (binary classification)
- **Inference Speed**: >10 samples/second
- **Training Time**: ~8-12 hours (full dataset)

### **Model Efficiency:**
- **Total Parameters**: ~1.3B (base model)
- **Trainable Parameters**: ~50M (LoRA + heads)
- **Trainable Percentage**: ~3.8%
- **Memory Usage**: ~12GB VRAM (training), ~6GB (inference)

## 🔧 Configuration Options

### **LoRA Ranks:**
```python
# High performance (recommended)
food_rank = 64      # Complex task, nhiều categories
harmful_rank = 32   # Simple binary task

# Balanced efficiency
food_rank = 32
harmful_rank = 16

# Maximum performance
food_rank = 128
harmful_rank = 64
```

### **Training Hyperparameters:**
```python
# Optimal settings (từ Progressive lessons)
learning_rate = 2e-4        # Base LR cho LoRA
head_lr_multiplier = 2.0    # 2x LR cho classification heads
batch_size = 8              # Balanced cho 12GB VRAM
epochs = 12                 # Sufficient convergence
scheduler = "OneCycleLR"    # Stable training
warmup_ratio = 0.1          # 10% warmup
```

### **Loss Configuration:**
```python
# Multi-task loss weights
food_weight = 1.0           # Base weight
harmful_weight = 1.2        # Slightly higher (safety critical)
focal_gamma = 2.0           # Handle imbalanced data
label_smoothing = 0.1       # Regularization
```

## 📁 Project Structure

```
Multi_LoRA_Finetune/
├── multi_task_dataset_pipeline.py    # Dataset loading & preprocessing
├── multi_lora_architecture.py        # Multi-LoRA model architecture
├── multi_lora_trainer.py            # Training logic với lessons learned
├── run_multi_lora_training.py       # Main training script
├── optimization_and_testing.py      # Hyperparameter search & evaluation
├── README.md                         # This file
└── results/                          # Training outputs
    ├── experiment_name/
    │   ├── final_model/              # Trained LoRA adapters
    │   ├── checkpoints/              # Training checkpoints
    │   ├── training_results.json     # Metrics & history
    │   └── experiment_config.json    # Configuration used
    └── optimization_results/         # Hyperparameter search results
```

## 🎯 Advanced Usage

### **Custom Harmful Dataset:**
```python
# Format cho harmful dataset
harmful_df = pd.DataFrame({
    'image_url': ['url1', 'url2', ...],
    'label': ['safe', 'harmful', ...],
    'harmful_type': ['nudity', 'violence', ...],  # Optional
    'confidence': [0.9, 0.8, ...]                 # Optional
})
harmful_df.to_csv('custom_harmful_dataset.csv', index=False)
```

### **Resume Training:**
```bash
python run_multi_lora_training.py \
  --resume results/experiment_name/checkpoints/checkpoint_epoch_5.pt \
  --epochs 15  # Continue to epoch 15
```

### **Evaluation Only:**
```python
from optimization_and_testing import MultiLoRAEvaluator

evaluator = MultiLoRAEvaluator("results/experiment_name/final_model")
eval_results = evaluator.evaluate_on_test_set(test_loader)
speed_results = evaluator.benchmark_inference_speed(test_loader)
```

## 🔍 Monitoring & Debugging

### **Training Logs:**
- `multi_lora_training.log`: Detailed training logs
- `training_results.json`: Metrics history
- Real-time progress trong terminal

### **Key Metrics to Watch:**
- **Loss convergence**: Both tasks should decrease steadily
- **Task balance**: Similar performance improvement rates
- **Memory usage**: Should be stable (no leaks)
- **Gradient norms**: Should be reasonable (<10)

### **Common Issues & Solutions:**
```python
# CUDA OOM
→ Reduce batch_size or LoRA ranks

# Poor food classification accuracy
→ Increase food_rank to 128, more epochs

# Poor harmful detection accuracy  
→ Check dataset balance, increase harmful_weight

# Training instability
→ Reduce learning_rate, increase warmup_ratio

# Slow convergence
→ Increase learning_rate, check data quality
```

## 📊 Comparison với Progressive Method

| Aspect | Progressive Method | Multi-LoRA Method |
|--------|-------------------|-------------------|
| **Tasks** | Single (food only) | Multi (food + harmful) |
| **Architecture** | Full model finetune | LoRA adapters |
| **Parameters** | ~1.3B trainable | ~50M trainable |
| **Memory** | ~20GB VRAM | ~12GB VRAM |
| **Training Time** | 6-8 hours | 8-12 hours |
| **Flexibility** | Single purpose | Multi-purpose |
| **Efficiency** | Lower | Higher |

## 🎉 Expected Results

### **After Optimization:**
- **Best Food Accuracy**: 85-90%
- **Best Harmful Accuracy**: 95-98%
- **Combined Performance**: Excellent on both tasks
- **Production Ready**: Fast inference, stable performance

### **Use Cases:**
1. **Content Moderation**: Tự động filter harmful content
2. **Food Recognition**: Vietnamese cuisine identification
3. **Safety Systems**: Combined food + safety detection
4. **Educational Apps**: Safe food learning platforms

---

**🚀 Multi-LoRA approach mang lại hiệu quả cao với resource ít hơn, phù hợp cho production deployment!**
