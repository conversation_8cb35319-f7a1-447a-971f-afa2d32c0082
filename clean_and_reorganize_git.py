#!/usr/bin/env python3
"""
Script để clean Git repository và tổ chức lại code

Bước 1: Gỗ bỏ tất cả code cũ trên Git
Bước 2: Tổ chức lại project structure
Bước 3: Push code mới lên Git
"""

import os
import shutil
from pathlib import Path
import subprocess

def run_command(command, description=""):
    """Chạy command và hiển thị kết quả"""
    print(f"🔄 {description}")
    print(f"   Command: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
        else:
            print(f"   ❌ Error: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def backup_current_code():
    """Backup code hiện tại"""
    print("\n📦 STEP 1: Backup current code")
    
    backup_dir = Path("../VLM_Parser_backup")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    shutil.copytree(".", backup_dir, ignore=shutil.ignore_patterns('.git'))
    print(f"✅ Code backed up to: {backup_dir}")

def clean_git_repository():
    """Gỡ bỏ tất cả file trên Git (giữ lại .git)"""
    print("\n🧹 STEP 2: Clean Git repository")
    
    # Remove all files except .git
    for item in Path(".").iterdir():
        if item.name != ".git" and item.name != "clean_and_reorganize_git.py":
            if item.is_dir():
                shutil.rmtree(item)
                print(f"   🗑️ Removed directory: {item.name}")
            else:
                item.unlink()
                print(f"   🗑️ Removed file: {item.name}")
    
    print("✅ Repository cleaned (kept .git folder)")

def create_organized_structure():
    """Tạo cấu trúc project mới"""
    print("\n🏗️ STEP 3: Create organized structure")
    
    # Tạo các thư mục chính
    directories = [
        "src",
        "models", 
        "data",
        "tests",
        "docs",
        "notebooks",
        "scripts",
        "configs"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"   📁 Created: {dir_name}/")
    
    print("✅ Directory structure created")

def copy_organized_code():
    """Copy code đã tổ chức từ backup"""
    print("\n📋 STEP 4: Copy organized code")
    
    backup_dir = Path("../VLM_Parser_backup")
    
    # Copy organized files
    files_to_copy = {
        "src/__init__.py": backup_dir / "src" / "__init__.py",
        "src/preprocessing.py": backup_dir / "src" / "preprocessing.py", 
        "src/finetune_multi_lora.py": backup_dir / "src" / "finetune_multi_lora.py",
        "requirements.txt": backup_dir / "requirements.txt",
        "main.py": backup_dir / "main.py",
        ".gitignore": backup_dir / ".gitignore",
        "README.md": backup_dir / "README_NEW.md",
        "PROJECT_STRUCTURE.md": backup_dir / "PROJECT_STRUCTURE.md",
        "tests/__init__.py": backup_dir / "tests" / "__init__.py",
        "tests/test_preprocessing.py": backup_dir / "tests" / "test_preprocessing.py",
        "docs/architecture.md": backup_dir / "docs" / "architecture.md",
        "notebooks/finetune_experiments.ipynb": backup_dir / "notebooks" / "finetune_experiments.ipynb",
        "models/README.md": backup_dir / "models" / "README.md",
        "data/example_data.csv": backup_dir / "data" / "example_data.csv"
    }
    
    for dest, src in files_to_copy.items():
        if src.exists():
            dest_path = Path(dest)
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dest_path)
            print(f"   📄 Copied: {dest}")
    
    print("✅ Organized code copied")

def create_additional_files():
    """Tạo các file bổ sung"""
    print("\n📝 STEP 5: Create additional files")
    
    # .gitattributes cho Git LFS
    gitattributes_content = """# Git LFS tracking
*.safetensors filter=lfs diff=lfs merge=lfs -text
*.bin filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.pt filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.model filter=lfs diff=lfs merge=lfs -text

# Large data files
*.csv filter=lfs diff=lfs merge=lfs -text
*.json filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
"""
    
    with open(".gitattributes", "w", encoding="utf-8") as f:
        f.write(gitattributes_content)
    print("   📄 Created: .gitattributes")
    
    # LICENSE
    license_content = """MIT License

Copyright (c) 2024 VLM Parser Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"""
    
    with open("LICENSE", "w", encoding="utf-8") as f:
        f.write(license_content)
    print("   📄 Created: LICENSE")
    
    # CHANGELOG.md
    changelog_content = """# Changelog

All notable changes to this project will be documented in this file.

## [1.0.0] - 2024-08-11

### Added
- Organized project structure with clean src/ modules
- Multi-LoRA fine-tuning implementation
- Progressive training methodology
- Data preprocessing utilities
- Model testing framework
- Web UI for model interaction
- Comprehensive documentation
- Unit tests structure
- Git LFS support for large files

### Changed
- Restructured codebase for better maintainability
- Improved Git workflow with proper .gitignore
- Enhanced documentation and README

### Technical
- Python 3.8+ support
- PyTorch 2.0+ compatibility
- Transformers 4.30+ integration
- PEFT library for LoRA implementation
"""
    
    with open("CHANGELOG.md", "w", encoding="utf-8") as f:
        f.write(changelog_content)
    print("   📄 Created: CHANGELOG.md")
    
    print("✅ Additional files created")

def main():
    """Main function"""
    print("🚀 VLM Parser - Clean & Reorganize Git Repository")
    print("=" * 60)
    
    try:
        # Step 1: Backup
        backup_current_code()
        
        # Step 2: Clean Git
        clean_git_repository()
        
        # Step 3: Create structure
        create_organized_structure()
        
        # Step 4: Copy organized code
        copy_organized_code()
        
        # Step 5: Create additional files
        create_additional_files()
        
        print("\n🎉 SUCCESS! Repository cleaned and reorganized")
        print("\n📋 Next steps:")
        print("   1. Review the new structure")
        print("   2. Run the Git commands from 'git_push_commands.txt'")
        print("   3. Add training folders later as needed")
        
        # Tạo file với Git commands
        create_git_commands_file()
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        print("   Check the backup in ../VLM_Parser_backup")

def create_git_commands_file():
    """Tạo file với các lệnh Git"""
    commands = """# Git Commands để push VLM project đã reorganize

## BƯỚC 1: Kiểm tra trạng thái
git status

## BƯỚC 2: Cài đặt Git LFS (nếu chưa có)
git lfs install

## BƯỚC 3: Add tất cả file mới
git add .

## BƯỚC 4: Commit với message rõ ràng
git commit -m "Complete project reorganization

- Clean repository structure
- Add organized src/ modules
- Implement proper Git LFS support
- Add comprehensive documentation
- Create professional project layout
- Ready for collaborative development

Features:
- Multi-LoRA fine-tuning
- Progressive training methods
- Data preprocessing pipeline
- Model testing framework
- Web UI interface
- Unit tests structure"

## BƯỚC 5: Push lên remote
git push origin master

## BƯỚC 6: Tạo release tag (optional)
git tag -a v1.0.0 -m "First clean release - Organized VLM Parser"
git push origin v1.0.0

## BƯỚC 7: Kiểm tra kết quả
git log --oneline -3
git ls-files

## Lưu ý:
# - Repository đã được clean hoàn toàn
# - Chỉ code organized được push
# - Large files sẽ dùng Git LFS
# - Training folders sẽ add sau

## Để add training folders sau:
# git add Multi_LoRA_Finetune/
# git add Progressive_Method_Finetune/
# git add Method_For_Test_With_Model_VinternV3.5/
# git add UI_For_VLM/
# git commit -m "Add original training implementations"
# git push origin master
"""
    
    with open("git_push_commands.txt", "w", encoding="utf-8") as f:
        f.write(commands)
    print("   📄 Created: git_push_commands.txt")

if __name__ == "__main__":
    main()
