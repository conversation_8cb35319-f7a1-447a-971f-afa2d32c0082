#!/usr/bin/env python3
"""
Main entry point for VLM Model project

This script provides a command-line interface to run different components:
- Data preprocessing
- Model training (Multi-LoRA, Progressive)
- Model testing
- UI launch
"""

import argparse
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from preprocessing import PreprocessingPipeline
from finetune_multi_lora import create_multi_lora_model, MultiLoRATrainer
# from finetune_progressive import ProgressiveTrainer
# from test_methods import ModelTester
# from ui import launch_ui

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description="VLM Model - Vietnamese Food Classification")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Preprocessing command
    preprocess_parser = subparsers.add_parser("preprocess", help="Run data preprocessing")
    preprocess_parser.add_argument("--input", required=True, help="Input CSV file")
    preprocess_parser.add_argument("--output", required=True, help="Output CSV file")
    preprocess_parser.add_argument("--samples", type=int, default=32000, help="Target number of samples")
    
    # Training commands
    train_parser = subparsers.add_parser("train", help="Train model")
    train_subparsers = train_parser.add_subparsers(dest="train_method", help="Training methods")
    
    # Multi-LoRA training
    multilora_parser = train_subparsers.add_parser("multi-lora", help="Multi-LoRA training")
    multilora_parser.add_argument("--model", default="Viet-Mistral/Viet-Mistral-7B-v0.1", help="Base model name")
    multilora_parser.add_argument("--data", required=True, help="Training data path")
    multilora_parser.add_argument("--output", default="./models/multi_lora", help="Output directory")
    
    # Progressive training
    progressive_parser = train_subparsers.add_parser("progressive", help="Progressive training")
    progressive_parser.add_argument("--model", default="Viet-Mistral/Viet-Mistral-7B-v0.1", help="Base model name")
    progressive_parser.add_argument("--data", required=True, help="Training data path")
    progressive_parser.add_argument("--output", default="./models/progressive", help="Output directory")
    
    # Testing command
    test_parser = subparsers.add_parser("test", help="Test model")
    test_parser.add_argument("--model", required=True, help="Model path")
    test_parser.add_argument("--data", required=True, help="Test data path")
    test_parser.add_argument("--output", default="./test_results", help="Output directory")
    
    # UI command
    ui_parser = subparsers.add_parser("ui", help="Launch web UI")
    ui_parser.add_argument("--model", required=True, help="Model path")
    ui_parser.add_argument("--port", type=int, default=8000, help="Port number")
    
    args = parser.parse_args()
    
    if args.command == "preprocess":
        logger.info("Starting data preprocessing...")
        pipeline = PreprocessingPipeline(target_samples=args.samples)
        result = pipeline.run_full_pipeline(args.input, args.output)
        logger.info(f"Preprocessing completed. Output: {args.output}")
        
    elif args.command == "train":
        if args.train_method == "multi-lora":
            logger.info("Starting Multi-LoRA training...")
            # Implementation for Multi-LoRA training
            logger.info("Multi-LoRA training completed")
            
        elif args.train_method == "progressive":
            logger.info("Starting Progressive training...")
            # Implementation for Progressive training
            logger.info("Progressive training completed")
            
    elif args.command == "test":
        logger.info("Starting model testing...")
        # Implementation for model testing
        logger.info("Model testing completed")
        
    elif args.command == "ui":
        logger.info("Launching web UI...")
        # Implementation for UI launch
        logger.info("Web UI launched")
        
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
