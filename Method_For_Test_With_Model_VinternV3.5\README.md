## README — <PERSON><PERSON><PERSON> phương pháp finetune đã thử nghiệm và so sánh

<PERSON>h<PERSON> mục: Method_For_Test_With_Model_VinternV3.5/test_vintern_training/TestFinetuneMethod

Bao gồm nhiều chiến lược finetune VLM/Vintern (LLM head/Adapter/Prompt/…): LoRA, QLoRA, DoRA, Progressive, Multitask, Hybrid, CLIP-LoRA, Visual Prompt Tuning, v.v.

---

## 1) Tổng quan phương pháp và tham số quan trọng

- Progressive Training
  - Ý tưởng: curriculum learning theo độ khó + balanced sampler để tối đa hóa generalization
  - Tham số: số level (3), lr giảm nhẹ theo level; batch đa dạng category; scheduler warmup
  - Hiện thực: Finetune_Progressive.py có cấu hình LoRA progressive cho language_model, in trainable params

- LoRA (Low-Rank Adaptation)
  - Ý tưởng: chèn ma trận low-rank vào các module attention/MLP của LLM để họ<PERSON>, ít tham số
  - <PERSON>ham số: rank r, alpha, target_modules (q,k,v,o,gate,up,down), dropout
  - Hiện thực: Finetune_Lora.py áp dụng LoRA vào model.language_model, train CE đơn giản trên text

- QLoRA
  - Ý tưởng: lượng tử hóa 4-bit + LoRA để giảm VRAM; phù hợp máy yếu
  - Tham số: BitsAndBytesConfig (nf4, double quant), r/alpha cao hơn, dropout thấp; paged optimizers
  - Hiện thực: Finetune_Qlora.py setup 4-bit + PEFT cho language_model, dataset high-quality prompt template

- DoRA / SAM LoRA / Adapter biến thể
  - Ý tưởng: biến thể của LoRA hoặc adapter layer để cải thiện ổn định/hội tụ
  - Tham số: rank, alpha, vị trí chèn; đôi khi tối ưu thêm trọng số hướng chuẩn hóa
  - Hiện thực: các file tương ứng trong thư mục (xem code để biết config chi tiết)

- Multi-Modal Adapter / VRGAdapter / ClipFit / CLIP-LoRA / LIT / Visual Prompt Tuning
  - Ý tưởng: can thiệp vào nhánh vision-text hoặc dùng prompt/adapter để điều chỉnh biểu diễn đa phương thức
  - Tham số: số lớp adapter, vị trí chèn (vision enc/neck/llm), prompt length, lr head vs base
  - Hiện thực: các script minh họa pipeline, batch nhỏ để test tính khả thi

---

## 2) Cách chạy thử nhanh một phương pháp (ví dụ LoRA)

```bash
cd Method_For_Test_With_Model_VinternV3.5/test_vintern_training/TestFinetuneMethod
python Finetune_Lora.py
```

Ghi chú:
- Các script test này thường dùng tập nhỏ (100–300 mẫu) để kiểm chứng pipeline/việc áp LoRA/QLoRA có hoạt động, không nhằm benchmark tuyệt đối.
- Với Progressive Training chính thức để train nghiêm túc, dùng thư mục Progressive_Method_Finetune/ …

---

## 3) Bảng xếp hạng phương pháp (do bạn cung cấp)

Tăng dữ liệu giảm overfit Progressive Training

Rank | Approach | Orig Samples | Orig Loss | Full Loss | Val Loss | Accuracy | Improvement | Rank Change
- 1 | Progressive Training | 200 | 1.1190 | 0.2467 | 0.4235 | 93–96% | 78.0% | ➡️ 0
- 2 | Multi-Modal Adapter | 250 | 1.6924 | 0.2734 | 0.1923 | 92–95% | 83.8% | ⬆️ +3
- 3 | LIT | 220 | 1.2238 | 0.2757 | 0.1931 | 92–95% | 77.5% | ⬇️ -1
- 4 | SAM LoRA | 240 | 2.1373 | 0.2757 | 0.1935 | 88–91% | 87.1% | ⬆️ +3
- 5 | VRGAdapter | 260 | 1.4110 | 0.2792 | 0.1917 | 90–93% | 80.2% | ⬇️ -2
- 6 | CLIP-LoRA | 220 | 5.0762 | 0.2815 | 0.2078 | 87–90% | 94.5% | ⬆️ +2
- 7 | DoRA | 280 | 1.5986 | 0.2845 | 0.1916 | 93–96% | 82.2% | ⬇️ -3
- 8 | ClipFit | 200 | 5.3792 | 0.2872 | 0.2095 | 86–89% | 94.7% | ⬆️ +1
- 9 | EHFR-Net | 300 | 1.8642 | 0.2883 | 0.2215 | 93–95% | 84.5% | ⬇️ -3
- 10 | Visual Prompt Tuning | 180 | 7.5885 | 0.3021 | 0.2285 | 81–84% | 96.0% | ➡️ 0

Giải thích cột:
- Orig Samples: số mẫu nhỏ ban đầu khi thử pipeline
- Orig/Full/Val Loss: loss ở các giai đoạn training khác nhau
- Accuracy: khoảng accuracy quan sát trên val/test
- Improvement: % cải thiện so với baseline trước đó
- Rank Change: thay đổi vị trí xếp hạng qua các vòng test

Lưu ý: bảng nhằm truyền đạt xu hướng tương đối giữa các phương pháp trong bối cảnh dữ liệu/setting hiện tại; khi thay đổi dataset, scale, hoặc hyperparameters, thứ hạng có thể thay đổi.

---

## 4) Khuyến nghị chọn phương pháp
- Muốn độ bền và tổng quát cao: Progressive Training (kết hợp sampler cân bằng + curriculum) là lựa chọn số 1
- Máy yếu/GPU hạn chế: QLoRA hoặc CLIP-LoRA
- Cần cải thiện ngữ cảnh đa phương thức: Multi-Modal Adapter/VRGAdapter/LIT
- Muốn nhanh, ít chỉnh: Visual Prompt Tuning/ClipFit (dễ thử nhưng có thể giới hạn trần hiệu năng)

---

## 5) Liên kết
- Progressive_Method_Finetune/README.md — hướng dẫn training bản chính (local/server)
- Preprocessing_Data/README.md — pipeline tiền xử lý & lấy sample
- UI_For_VLM/README.md — test nhanh model đã fine-tune trong UI
