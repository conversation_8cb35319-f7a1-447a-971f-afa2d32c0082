#!/usr/bin/env python3
"""
Hyperparameter Search cho Vintern Progressive Training
- Grid search cho learning rate, epochs, batch size
- Bayesian optimization cho advanced parameters
- Auto early stopping và model selection
"""

import torch
import json
import logging
import itertools
import time
from pathlib import Path
import numpy as np
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HyperparameterSearch:
    """Hyperparameter search cho progressive training"""
    
    def __init__(self, base_output_dir="hyperparameter_search"):
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        self.results = []
        
    def define_search_space(self):
        """Define hyperparameter search space"""
        return {
            'learning_rates': [1e-5, 2e-5, 3e-5, 5e-5],
            'epochs_per_level': [3, 4, 5, 6],
            'batch_sizes': [6, 8, 12, 16],
            'weight_decays': [0.001, 0.005, 0.01],
            'schedulers': ['OneCycleLR', 'CosineAnnealingLR', 'ConstantLR'],
            'warmup_ratios': [0.05, 0.1, 0.15],
            'gradient_clip_norms': [0.5, 1.0, 2.0]
        }
    
    def grid_search_basic(self, max_experiments=20):
        """Grid search cho basic hyperparameters"""
        search_space = self.define_search_space()
        
        # Basic grid: LR x Epochs x Scheduler
        basic_combinations = list(itertools.product(
            search_space['learning_rates'],
            search_space['epochs_per_level'],
            search_space['schedulers']
        ))
        
        # Limit experiments
        if len(basic_combinations) > max_experiments:
            # Sample randomly
            indices = np.random.choice(len(basic_combinations), max_experiments, replace=False)
            basic_combinations = [basic_combinations[i] for i in indices]
        
        logger.info(f"Starting grid search with {len(basic_combinations)} experiments")
        
        for i, (lr, epochs, scheduler) in enumerate(basic_combinations):
            logger.info(f"\nExperiment {i+1}/{len(basic_combinations)}")
            logger.info(f"LR: {lr}, Epochs: {epochs}, Scheduler: {scheduler}")
            
            config = {
                'learning_rate': lr,
                'epochs_per_level': epochs,
                'scheduler': scheduler,
                'batch_size': 8,  # Default
                'weight_decay': 0.01,  # Default
                'warmup_ratio': 0.1,  # Default
                'gradient_clip_norm': 1.0  # Default
            }
            
            result = self.run_experiment(config, experiment_id=f"grid_{i+1}")
            self.results.append(result)
            
            # Save intermediate results
            self.save_results()
            
            # Early termination if clearly bad
            if result['best_val_loss'] > 1.0:
                logger.info("Early termination: loss too high")
                continue
        
        return self.results
    
    def bayesian_search(self, n_experiments=15):
        """Bayesian optimization cho advanced search"""
        try:
            from skopt import gp_minimize
            from skopt.space import Real, Integer, Categorical
            from skopt.utils import use_named_args
        except ImportError:
            logger.warning("scikit-optimize not available, skipping Bayesian search")
            return []
        
        # Define search space for Bayesian optimization
        dimensions = [
            Real(1e-6, 1e-4, name='learning_rate', prior='log-uniform'),
            Integer(3, 8, name='epochs_per_level'),
            Real(0.001, 0.02, name='weight_decay', prior='log-uniform'),
            Real(0.05, 0.2, name='warmup_ratio'),
            Real(0.5, 2.0, name='gradient_clip_norm'),
            Categorical(['OneCycleLR', 'CosineAnnealingLR'], name='scheduler')
        ]
        
        @use_named_args(dimensions)
        def objective(**params):
            """Objective function for Bayesian optimization"""
            config = {
                'batch_size': 8,  # Fixed
                **params
            }
            
            result = self.run_experiment(config, experiment_id=f"bayes_{len(self.results)+1}")
            self.results.append(result)
            self.save_results()
            
            # Return loss to minimize
            return result['best_val_loss']
        
        logger.info(f"Starting Bayesian optimization with {n_experiments} experiments")
        
        # Run optimization
        result = gp_minimize(
            func=objective,
            dimensions=dimensions,
            n_calls=n_experiments,
            random_state=42,
            acq_func='EI'  # Expected Improvement
        )
        
        logger.info(f"Bayesian optimization completed")
        logger.info(f"Best parameters: {dict(zip([d.name for d in dimensions], result.x))}")
        logger.info(f"Best loss: {result.fun:.4f}")
        
        return self.results
    
    def run_experiment(self, config: Dict, experiment_id: str) -> Dict:
        """Run single training experiment"""
        start_time = time.time()
        
        # Create experiment directory
        exp_dir = self.base_output_dir / experiment_id
        exp_dir.mkdir(exist_ok=True)
        
        # Save config
        with open(exp_dir / "config.json", 'w') as f:
            json.dump(config, f, indent=2)
        
        try:
            # Import and run training
            from FinetuneWithProgressInLocal.ImprovedFinetuneMethod import ImprovedProgressiveTrainer
            
            trainer = ImprovedProgressiveTrainer(
                model_path="Vintern-1B-v3_5",
                output_dir=str(exp_dir)
            )
            
            # Apply config to trainer
            self.apply_config_to_trainer(trainer, config)
            
            # Run training
            results = trainer.enhanced_progressive_train(
                num_epochs_per_level=config['epochs_per_level']
            )
            
            # Extract metrics
            best_val_loss = results['best_overall_loss']
            total_time = results['total_time_hours']
            
            # Calculate score (lower is better)
            score = best_val_loss + 0.01 * total_time  # Penalize long training
            
            result = {
                'experiment_id': experiment_id,
                'config': config,
                'best_val_loss': best_val_loss,
                'total_time_hours': total_time,
                'score': score,
                'level_results': results['level_results'],
                'status': 'completed'
            }
            
            logger.info(f"Experiment {experiment_id} completed: loss={best_val_loss:.4f}, time={total_time:.2f}h")
            
        except Exception as e:
            logger.error(f"Experiment {experiment_id} failed: {e}")
            result = {
                'experiment_id': experiment_id,
                'config': config,
                'best_val_loss': float('inf'),
                'total_time_hours': (time.time() - start_time) / 3600,
                'score': float('inf'),
                'error': str(e),
                'status': 'failed'
            }
        
        return result
    
    def apply_config_to_trainer(self, trainer, config):
        """Apply hyperparameter config to trainer"""
        # This would need to be implemented based on trainer's API
        # For now, we'll modify the trainer's methods
        
        # Store config for use in training
        trainer.hp_config = config
        
        # Override get_enhanced_batch_size
        original_get_batch_size = trainer.get_enhanced_batch_size
        def new_get_batch_size(num_categories):
            return min(config['batch_size'], num_categories)
        trainer.get_enhanced_batch_size = new_get_batch_size
    
    def save_results(self):
        """Save search results"""
        results_file = self.base_output_dir / "search_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Save summary
        if self.results:
            summary = self.analyze_results()
            summary_file = self.base_output_dir / "search_summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
    
    def analyze_results(self) -> Dict:
        """Analyze search results"""
        if not self.results:
            return {}
        
        # Filter successful experiments
        successful = [r for r in self.results if r['status'] == 'completed']
        
        if not successful:
            return {'message': 'No successful experiments'}
        
        # Sort by score (lower is better)
        successful.sort(key=lambda x: x['score'])
        
        best = successful[0]
        worst = successful[-1]
        
        # Calculate statistics
        losses = [r['best_val_loss'] for r in successful]
        times = [r['total_time_hours'] for r in successful]
        
        summary = {
            'total_experiments': len(self.results),
            'successful_experiments': len(successful),
            'failed_experiments': len(self.results) - len(successful),
            'best_experiment': {
                'id': best['experiment_id'],
                'config': best['config'],
                'loss': best['best_val_loss'],
                'time': best['total_time_hours']
            },
            'statistics': {
                'loss_mean': np.mean(losses),
                'loss_std': np.std(losses),
                'loss_min': np.min(losses),
                'loss_max': np.max(losses),
                'time_mean': np.mean(times),
                'time_std': np.std(times)
            },
            'top_5_configs': [
                {
                    'id': r['experiment_id'],
                    'config': r['config'],
                    'loss': r['best_val_loss'],
                    'time': r['total_time_hours']
                }
                for r in successful[:5]
            ]
        }
        
        return summary
    
    def get_best_config(self) -> Dict:
        """Get best hyperparameter configuration"""
        if not self.results:
            return {}
        
        successful = [r for r in self.results if r['status'] == 'completed']
        if not successful:
            return {}
        
        best = min(successful, key=lambda x: x['score'])
        return best['config']

def main():
    """Main hyperparameter search"""
    logger.info("Starting Hyperparameter Search for Vintern Progressive Training")
    
    searcher = HyperparameterSearch()
    
    # Run grid search first
    logger.info("Phase 1: Grid Search")
    grid_results = searcher.grid_search_basic(max_experiments=12)
    
    # Run Bayesian optimization
    logger.info("Phase 2: Bayesian Optimization")
    bayes_results = searcher.bayesian_search(n_experiments=8)
    
    # Analyze final results
    summary = searcher.analyze_results()
    best_config = searcher.get_best_config()
    
    logger.info("\n" + "="*60)
    logger.info("HYPERPARAMETER SEARCH COMPLETED")
    logger.info("="*60)
    
    if best_config:
        logger.info("Best configuration found:")
        for key, value in best_config.items():
            logger.info(f"  {key}: {value}")
        
        logger.info(f"\nBest loss: {summary['best_experiment']['loss']:.4f}")
        logger.info(f"Training time: {summary['best_experiment']['time']:.2f} hours")
    
    logger.info(f"\nTotal experiments: {summary.get('total_experiments', 0)}")
    logger.info(f"Successful: {summary.get('successful_experiments', 0)}")
    logger.info(f"Failed: {summary.get('failed_experiments', 0)}")
    
    logger.info(f"\nResults saved to: {searcher.base_output_dir}")

if __name__ == "__main__":
    main()
