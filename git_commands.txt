# Git Commands để push VLM project đã tổ chức

## 1. <PERSON><PERSON><PERSON> tra trạng thái hiện tại
git status

## 2. Thê<PERSON> tất cả file mới (organized structure)
git add .

## 3. Commit với message rõ ràng
git commit -m "Organize project structure for better Git management

- Add organized src/ modules (preprocessing, multi-lora, progressive)
- Create proper .gitignore for large files
- Add requirements.txt with all dependencies
- Add main.py as single entry point
- Add tests/ and docs/ structure
- Preserve original folders for backward compatibility
- Ready for collaborative development"

## 4. Push lên remote repository
git push origin master

## 5. <PERSON><PERSON><PERSON> c<PERSON> conflict, pull trước rồi push lại
git pull origin master
git push origin master

## 6. Nếu cần force push (cẩn thận!)
git push -f origin master

## 7. <PERSON><PERSON><PERSON> tra kết quả
git log --oneline -5

## 8. Xem file nào đã được push
git ls-files

## 9. <PERSON><PERSON><PERSON> tra kích thước repository
git count-objects -vH

## 10. Nếu muốn tạo release tag
git tag -a v1.0.0 -m "First organized release"
git push origin v1.0.0

## Lưu ý:
# - File lớn (CSV, model) đã được ignore
# - Chỉ code và documentation được push
# - Original folders vẫn được giữ nguyên
# - Project structure giờ đã professional và dễ collaborate
