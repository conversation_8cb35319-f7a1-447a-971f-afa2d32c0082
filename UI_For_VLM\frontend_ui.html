<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vietnamese Food VLM - AI Food Recognition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .upload-section.dragover {
            border-color: #28a745;
            background: #d4edda;
        }

        .upload-area {
            cursor: pointer;
            padding: 20px;
        }

        .upload-icon {
            font-size: 4em;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #495057;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .query-section {
            margin: 20px 0;
        }

        .query-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .query-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .results-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
        }

        .results-title {
            font-size: 1.5em;
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
        }

        .prediction-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .prediction-card:hover {
            transform: translateY(-2px);
        }

        .prediction-card.top {
            border-left: 5px solid #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }

        .prediction-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }

        .prediction-confidence {
            font-size: 1.1em;
            color: #007bff;
            font-weight: 600;
        }

        .confidence-bar {
            width: 100%;
            height: 8px;
            background: #dee2e6;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            transition: width 0.5s ease;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-bar {
            background: #e9ecef;
            padding: 15px;
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍜 Vietnamese Food VLM</h1>
            <p>AI-Powered Vietnamese Food Recognition System</p>
        </div>

        <div class="status-bar">
            Backend Status: <span id="status">Checking...</span>
        </div>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-icon">📸</div>
                    <div class="upload-text">
                        Click here or drag & drop an image of Vietnamese food
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept="image/*">
                </div>
                
                <img id="imagePreview" class="image-preview" style="display: none;">
                
                <div class="query-section">
                    <label for="queryInput" style="display: block; margin-bottom: 10px; font-weight: bold;">
                        Ask about the food:
                    </label>
                    <input 
                        type="text" 
                        id="queryInput" 
                        class="query-input" 
                        placeholder="What Vietnamese food is this?"
                        value="What Vietnamese food is this?"
                    >
                </div>
                
                <button id="analyzeBtn" class="btn" onclick="analyzeImage()" disabled>
                    🔍 Analyze Food
                </button>
                
                <button class="btn" onclick="loadModel()">
                    🔄 Reload Model
                </button>
            </div>

            <div class="results-section">
                <div class="results-title">🎯 Analysis Results</div>
                <div id="results">
                    <div style="text-align: center; color: #6c757d; padding: 40px;">
                        Upload an image to see AI predictions
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let currentImage = null;

        // Check backend status
        async function checkStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                const statusElement = document.getElementById('status');
                if (data.status === 'healthy') {
                    statusElement.innerHTML = '<span class="status-online">Online ✅</span>';
                    statusElement.innerHTML += ` | Model: ${data.model_loaded ? 'Loaded' : 'Not Loaded'} | Categories: ${data.categories}`;
                } else {
                    statusElement.innerHTML = '<span class="status-offline">Offline ❌</span>';
                }
            } catch (error) {
                document.getElementById('status').innerHTML = '<span class="status-offline">Backend Offline ❌</span>';
            }
        }

        // Load model
        async function loadModel() {
            try {
                const response = await fetch(`${API_BASE}/load_model`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Model loaded successfully!');
                    checkStatus();
                } else {
                    alert('Failed to load model');
                }
            } catch (error) {
                alert('Error loading model: ' + error.message);
            }
        }

        // Handle file input
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImage = e.target.result;
                    const preview = document.getElementById('imagePreview');
                    preview.src = currentImage;
                    preview.style.display = 'block';
                    document.getElementById('analyzeBtn').disabled = false;
                };
                reader.readAsDataURL(file);
            }
        });

        // Drag and drop
        const uploadSection = document.querySelector('.upload-section');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        currentImage = e.target.result;
                        const preview = document.getElementById('imagePreview');
                        preview.src = currentImage;
                        preview.style.display = 'block';
                        document.getElementById('analyzeBtn').disabled = false;
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // Analyze image
        async function analyzeImage() {
            if (!currentImage) {
                alert('Please select an image first');
                return;
            }

            const query = document.getElementById('queryInput').value;
            const resultsDiv = document.getElementById('results');
            
            // Show loading
            resultsDiv.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Analyzing image with AI...</div>
                </div>
            `;

            try {
                const response = await fetch(`${API_BASE}/predict`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        image: currentImage,
                        text: query
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error-message">
                            Error: ${data.error || 'Unknown error occurred'}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error-message">
                        Network Error: ${error.message}
                    </div>
                `;
            }
        }

        // Display results
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            let html = `<div style="margin-bottom: 20px; font-style: italic; color: #6c757d;">
                Query: "${data.query}"
            </div>`;

            data.predictions.forEach((pred, index) => {
                const isTop = index === 0;
                html += `
                    <div class="prediction-card ${isTop ? 'top' : ''}">
                        <div class="prediction-name">
                            ${isTop ? '🏆 ' : ''}${pred.category}
                        </div>
                        <div class="prediction-confidence">
                            Confidence: ${pred.percentage}
                        </div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: ${pred.confidence * 100}%"></div>
                        </div>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            setInterval(checkStatus, 30000); // Check status every 30 seconds
        });
    </script>
</body>
</html>
