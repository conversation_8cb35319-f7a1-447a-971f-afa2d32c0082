#!/usr/bin/env python3
"""
IMPROVED Vintern Progressive Training với:
- Supervised Classification Loss (thay proxy loss)
- Multi-task Learning (classification + generation)
- Curriculum Learning adaptive
- Enhanced hyperparameters
- Better data utilization
"""

import torch
import json
import logging
import time
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from improved_loss_functions import load_improved_loss, create_category_mapping
from FinetuneWithProgressiveMethod import (
    CategoryBalancedDataset, 
    FixedBalancedBatchSampler,
    balanced_collate_fn,
    BalancedProgressiveTrainer
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('improved_vintern_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ImprovedProgressiveTrainer(BalancedProgressiveTrainer):
    """Enhanced trainer với improved loss functions"""
    
    def __init__(self, model_path="Vintern-1B-v3_5", output_dir="improved_vintern_training"):
        super().__init__(model_path, output_dir)
        self.improved_loss = None
        self.category_mapping = None
        
    def setup_improved_loss(self, categories):
        """Setup improved loss functions"""
        logger.info("Setting up improved loss functions...")
        
        self.category_mapping = create_category_mapping(categories)
        self.improved_loss, self.base_loss = load_improved_loss(
            self.tokenizer, categories, self.device
        )
        
        logger.info(f"Improved loss setup: {len(self.category_mapping)} categories")
        
    def enhanced_compute_loss(self, batch, epoch, total_epochs):
        """Enhanced loss computation với multi-task learning"""
        if self.improved_loss is None:
            # Fallback to original loss
            return super().enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)
        
        # Update curriculum learning
        self.improved_loss.update_epoch(epoch, total_epochs)
        
        # Compute improved loss
        try:
            loss, metrics = self.improved_loss(self.model, self.tokenizer, batch)
            
            # Log metrics occasionally
            if torch.rand(1).item() < 0.1:  # 10% of batches
                logger.debug(f"Loss metrics: {metrics}")
            
            return loss
            
        except Exception as e:
            logger.warning(f"Improved loss failed, fallback to original: {e}")
            return super().enhanced_compute_loss(self.model, self.tokenizer, batch, self.device)
    
    def get_enhanced_batch_size(self, num_categories):
        """Enhanced batch size calculation"""
        if not torch.cuda.is_available():
            return min(6, num_categories)  # Tăng từ 4
        
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory >= 8:
            return min(12, num_categories)  # Tăng từ 6
        elif gpu_memory >= 6:
            return min(8, num_categories)   # Tăng từ 4
        else:
            return min(4, num_categories)
    
    def enhanced_progressive_train(self, num_epochs_per_level=4):  # Tăng từ 2
        """Enhanced progressive training với improved loss"""
        logger.info("Starting ENHANCED PROGRESSIVE training...")
        start_time = time.time()
        
        # Load and prepare data
        df = self.load_and_prepare_data()
        train_df, val_df, test_df = self.split_data(df)
        
        # Setup improved loss
        all_categories = df['final_ultimate_category'].unique().tolist()
        self.setup_improved_loss(all_categories)
        
        num_categories = df['final_ultimate_category'].nunique()
        batch_size = self.get_enhanced_batch_size(num_categories)
        
        logger.info(f"Enhanced training config:")
        logger.info(f"  Categories: {num_categories}")
        logger.info(f"  Batch size: {batch_size}")
        logger.info(f"  Epochs per level: {num_epochs_per_level}")
        logger.info(f"  Improved loss: Multi-task + Curriculum")
        
        all_results = []
        best_overall_loss = float('inf')
        
        # Progressive training through 3 levels
        for difficulty_level in [1, 2, 3]:
            logger.info(f"\n{'='*60}")
            logger.info(f"ENHANCED LEVEL {difficulty_level}/3")
            logger.info(f"{'='*60}")
            
            train_dataset, val_dataset, train_loader, val_loader = self.create_balanced_dataloaders(
                train_df, val_df, batch_size, difficulty_level
            )
            
            # Enhanced learning rate strategy
            base_lr = 3e-5  # Tăng từ 2e-5
            lr = base_lr * (0.9 ** (difficulty_level - 1))  # Gentler reduction
            
            optimizer = torch.optim.AdamW(
                [p for p in self.model.parameters() if p.requires_grad],
                lr=lr,
                weight_decay=0.005,  # Giảm từ 0.01
                eps=1e-8,
                betas=(0.9, 0.999)
            )
            
            # Enhanced scheduler với warmup
            total_steps = len(train_loader) * num_epochs_per_level
            warmup_steps = total_steps // 10
            
            scheduler = torch.optim.lr_scheduler.OneCycleLR(
                optimizer,
                max_lr=lr,
                total_steps=total_steps,
                pct_start=0.1,  # 10% warmup
                anneal_strategy='cos'
            )
            
            logger.info(f"Level {difficulty_level} config:")
            logger.info(f"  Categories: {len(train_dataset.filtered_categories)}")
            logger.info(f"  LR: {lr:.2e}, Warmup steps: {warmup_steps}")
            logger.info(f"  Total steps: {total_steps}")
            
            level_results = self._enhanced_train_level(
                difficulty_level, num_epochs_per_level, train_loader, val_loader,
                optimizer, scheduler, train_dataset, val_dataset
            )
            
            all_results.append(level_results)
            
            if level_results['best_val_loss'] < best_overall_loss:
                best_overall_loss = level_results['best_val_loss']
                self.model.save_pretrained(f"{self.output_dir}/enhanced_best_model")
                self.tokenizer.save_pretrained(f"{self.output_dir}/enhanced_best_model")
                logger.info(f"New enhanced best model saved (loss: {best_overall_loss:.4f})")
        
        # Save final model
        self.model.save_pretrained(f"{self.output_dir}/enhanced_final_model")
        self.tokenizer.save_pretrained(f"{self.output_dir}/enhanced_final_model")
        
        total_time = time.time() - start_time
        
        # Final results
        final_results = {
            'enhanced_progressive_training': True,
            'improved_loss_functions': True,
            'total_categories': num_categories,
            'difficulty_levels': 3,
            'epochs_per_level': num_epochs_per_level,
            'batch_size': batch_size,
            'train_samples': len(train_df),
            'val_samples': len(val_df),
            'test_samples': len(test_df),
            'total_time_hours': total_time / 3600,
            'best_overall_loss': float(best_overall_loss),
            'level_results': all_results,
            'improvements': [
                'Multi-task loss (classification + generation)',
                'Curriculum learning adaptive',
                'Enhanced learning rate with OneCycleLR',
                'Increased epochs per level',
                'Better batch size optimization'
            ]
        }
        
        with open(f"{self.output_dir}/enhanced_training_results.json", 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n{'='*60}")
        logger.info("ENHANCED PROGRESSIVE TRAINING COMPLETED")
        logger.info(f"Best overall loss: {best_overall_loss:.4f}")
        logger.info(f"Total time: {total_time/3600:.2f} hours")
        logger.info(f"Improvements applied: {len(final_results['improvements'])}")
        logger.info(f"{'='*60}")
        
        return final_results
    
    def _enhanced_train_level(self, level, num_epochs, train_loader, val_loader,
                             optimizer, scheduler, train_dataset, val_dataset):
        """Enhanced training level với improved loss"""
        level_train_losses = []
        level_val_losses = []
        best_level_loss = float('inf')
        
        for epoch in range(1, num_epochs + 1):
            logger.info(f"\nLevel {level}, Epoch {epoch}/{num_epochs}")
            
            # Training
            self.model.train()
            epoch_loss = 0.0
            num_batches = 0
            category_seen = set()
            
            for batch_idx, batch in enumerate(train_loader):
                if batch_idx >= len(train_loader):
                    break
                    
                batch_categories = set(batch['categories'])
                category_seen.update(batch_categories)
                
                # Enhanced loss computation
                loss = self.enhanced_compute_loss(batch, epoch, num_epochs)
                
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()
                
                epoch_loss += loss.item()
                num_batches += 1
                
                # Memory cleanup
                del loss, batch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                if (batch_idx + 1) % 10 == 0:
                    avg_loss = epoch_loss / num_batches
                    lr = scheduler.get_last_lr()[0]
                    logger.info(f"L{level} E{epoch} Batch {batch_idx+1}/{len(train_loader)}, "
                               f"Loss: {avg_loss:.4f}, LR: {lr:.2e}")
            
            avg_train_loss = epoch_loss / max(num_batches, 1)
            level_train_losses.append(avg_train_loss)
            
            # Validation
            self.model.eval()
            val_loss = 0.0
            val_batches = 0
            
            with torch.no_grad():
                for val_batch_idx, batch in enumerate(val_loader):
                    if val_batch_idx >= len(val_loader):
                        break
                        
                    loss = self.enhanced_compute_loss(batch, epoch, num_epochs)
                    val_loss += loss.item()
                    val_batches += 1
                    
                    del loss, batch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
            
            avg_val_loss = val_loss / max(val_batches, 1)
            level_val_losses.append(avg_val_loss)
            
            if avg_val_loss < best_level_loss:
                best_level_loss = avg_val_loss
                self.model.save_pretrained(f"{self.output_dir}/enhanced_level_{level}_best")
                self.tokenizer.save_pretrained(f"{self.output_dir}/enhanced_level_{level}_best")
            
            logger.info(f"Level {level} Epoch {epoch}: Train={avg_train_loss:.4f}, "
                       f"Val={avg_val_loss:.4f}, Best={best_level_loss:.4f}")
        
        return {
            'level': level,
            'num_epochs': num_epochs,
            'categories_count': len(train_dataset.filtered_categories),
            'train_losses': level_train_losses,
            'val_losses': level_val_losses,
            'best_val_loss': float(best_level_loss),
            'final_train_loss': float(level_train_losses[-1]) if level_train_losses else float('inf'),
            'final_val_loss': float(level_val_losses[-1]) if level_val_losses else float('inf')
        }

def main():
    """Main enhanced training function"""
    logger.info("="*60)
    logger.info("STARTING ENHANCED PROGRESSIVE VINTERN TRAINING")
    logger.info("="*60)
    logger.info("ENHANCEMENTS:")
    logger.info("✅ Multi-task loss (classification + generation)")
    logger.info("✅ Curriculum learning adaptive")
    logger.info("✅ Enhanced learning rate with OneCycleLR")
    logger.info("✅ Increased epochs per level (4 instead of 2)")
    logger.info("✅ Better batch size optimization")
    logger.info("="*60)
    
    trainer = ImprovedProgressiveTrainer(
        model_path="Vintern-1B-v3_5",
        output_dir="enhanced_vintern_training"
    )
    
    results = trainer.enhanced_progressive_train(num_epochs_per_level=4)
    
    print("\n" + "="*60)
    print("🎉 ENHANCED PROGRESSIVE TRAINING COMPLETED!")
    print("="*60)
    print(f"✅ Best validation loss: {results['best_overall_loss']:.4f}")
    print(f"✅ Total training time: {results['total_time_hours']:.2f} hours")
    print(f"✅ Enhancements applied: {len(results['improvements'])}")
    print("="*60)

if __name__ == "__main__":
    main()
