#!/usr/bin/env python3
"""
Improved Loss Functions cho Vintern VLM Training
- Supervised Cross-Entropy Loss (thay proxy loss)
- Contrastive Learning Loss
- Multi-task Loss (classification + generation)
- Focal Loss cho imbalanced categories
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoTokenizer
import numpy as np
from typing import Dict, List, Optional

class ImprovedVLMLoss:
    """Improved loss functions cho VLM training"""
    
    def __init__(self, tokenizer, category_mapping: Dict[str, int], device='cuda'):
        self.tokenizer = tokenizer
        self.category_mapping = category_mapping
        self.num_categories = len(category_mapping)
        self.device = device
        
        # Classification head cho categories
        self.category_classifier = nn.Linear(
            4096,  # Vintern hidden size
            self.num_categories
        ).to(device)
        
        # Loss functions
        self.ce_loss = nn.CrossEntropyLoss(label_smoothing=0.1)
        self.focal_loss = FocalLoss(alpha=1.0, gamma=2.0)
        
    def supervised_classification_loss(self, model, images, categories):
        """Supervised classification loss - thay thế proxy loss"""
        batch_size = images.size(0)
        
        # Extract visual features từ model
        with torch.no_grad():
            # Get image embeddings từ vision encoder
            image_features = model.vision_model(images)
            # Pool features
            pooled_features = image_features.last_hidden_state.mean(dim=1)  # [B, hidden_size]
        
        # Classification logits
        logits = self.category_classifier(pooled_features)  # [B, num_categories]
        
        # Convert category names to indices
        category_indices = []
        for cat in categories:
            if cat in self.category_mapping:
                category_indices.append(self.category_mapping[cat])
            else:
                category_indices.append(0)  # Unknown category
        
        targets = torch.tensor(category_indices, device=self.device)
        
        # Compute loss
        loss = self.ce_loss(logits, targets)
        
        # Accuracy for monitoring
        pred_indices = torch.argmax(logits, dim=1)
        accuracy = (pred_indices == targets).float().mean()
        
        return loss, accuracy.item()
    
    def contrastive_loss(self, model, images, categories, temperature=0.07):
        """Contrastive learning loss cho similar categories"""
        batch_size = images.size(0)
        
        # Get image embeddings
        with torch.no_grad():
            image_features = model.vision_model(images)
            embeddings = image_features.last_hidden_state.mean(dim=1)  # [B, hidden_size]
            embeddings = F.normalize(embeddings, p=2, dim=1)
        
        # Compute similarity matrix
        sim_matrix = torch.matmul(embeddings, embeddings.T) / temperature  # [B, B]
        
        # Create positive/negative masks
        category_tensor = torch.tensor([
            self.category_mapping.get(cat, 0) for cat in categories
        ], device=self.device)
        
        # Positive pairs: same category
        pos_mask = (category_tensor.unsqueeze(0) == category_tensor.unsqueeze(1)).float()
        pos_mask.fill_diagonal_(0)  # Remove self-similarity
        
        # Negative pairs: different categories
        neg_mask = 1 - pos_mask
        neg_mask.fill_diagonal_(0)
        
        # Contrastive loss
        pos_sim = sim_matrix * pos_mask
        neg_sim = sim_matrix * neg_mask
        
        # InfoNCE loss
        pos_exp = torch.exp(pos_sim).sum(dim=1)
        neg_exp = torch.exp(neg_sim).sum(dim=1)
        
        loss = -torch.log(pos_exp / (pos_exp + neg_exp + 1e-8)).mean()
        
        return loss
    
    def generation_loss(self, model, tokenizer, images, instructions, responses):
        """Standard generation loss cho text output"""
        batch_size = images.size(0)
        total_loss = 0.0
        valid_samples = 0
        
        for i in range(batch_size):
            try:
                # Tokenize target response
                target_tokens = tokenizer(
                    responses[i], 
                    return_tensors="pt", 
                    padding=True, 
                    truncation=True,
                    max_length=128
                ).to(self.device)
                
                # Forward pass
                outputs = model(
                    images=images[i:i+1],
                    input_ids=target_tokens.input_ids,
                    attention_mask=target_tokens.attention_mask,
                    labels=target_tokens.input_ids
                )
                
                if hasattr(outputs, 'loss') and outputs.loss is not None:
                    total_loss += outputs.loss.item()
                    valid_samples += 1
                    
            except Exception as e:
                continue
        
        avg_loss = total_loss / max(valid_samples, 1)
        return torch.tensor(avg_loss, requires_grad=True, device=self.device)
    
    def multi_task_loss(self, model, tokenizer, batch, 
                       classification_weight=0.5, 
                       contrastive_weight=0.3, 
                       generation_weight=0.2):
        """Multi-task loss combining classification, contrastive, and generation"""
        images = batch['images']
        categories = batch['categories']
        instructions = batch['instructions']
        responses = batch['responses']
        
        # 1. Classification loss
        cls_loss, accuracy = self.supervised_classification_loss(model, images, categories)
        
        # 2. Contrastive loss
        cont_loss = self.contrastive_loss(model, images, categories)
        
        # 3. Generation loss (if model supports it)
        try:
            gen_loss = self.generation_loss(model, tokenizer, images, instructions, responses)
        except:
            gen_loss = torch.tensor(0.0, device=self.device)
        
        # Combined loss
        total_loss = (classification_weight * cls_loss + 
                     contrastive_weight * cont_loss + 
                     generation_weight * gen_loss)
        
        return total_loss, {
            'classification_loss': cls_loss.item(),
            'contrastive_loss': cont_loss.item(),
            'generation_loss': gen_loss.item(),
            'accuracy': accuracy,
            'total_loss': total_loss.item()
        }

class FocalLoss(nn.Module):
    """Focal Loss cho imbalanced categories"""
    
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class CurriculumLoss:
    """Curriculum learning với adaptive difficulty"""
    
    def __init__(self, base_loss_fn, difficulty_schedule='linear'):
        self.base_loss_fn = base_loss_fn
        self.difficulty_schedule = difficulty_schedule
        self.current_epoch = 0
        self.total_epochs = 100
    
    def update_epoch(self, epoch, total_epochs):
        self.current_epoch = epoch
        self.total_epochs = total_epochs
    
    def get_difficulty_weight(self):
        """Tính difficulty weight theo epoch"""
        progress = self.current_epoch / self.total_epochs
        
        if self.difficulty_schedule == 'linear':
            return progress
        elif self.difficulty_schedule == 'exponential':
            return 1 - np.exp(-3 * progress)
        else:
            return 1.0
    
    def __call__(self, model, tokenizer, batch):
        """Apply curriculum learning"""
        difficulty_weight = self.get_difficulty_weight()
        
        # Easy samples: high confidence predictions
        # Hard samples: low confidence predictions
        
        loss, metrics = self.base_loss_fn(model, tokenizer, batch)
        
        # Adjust loss based on difficulty
        adjusted_loss = loss * (0.5 + 0.5 * difficulty_weight)
        
        metrics['difficulty_weight'] = difficulty_weight
        metrics['adjusted_loss'] = adjusted_loss.item()
        
        return adjusted_loss, metrics

def create_category_mapping(categories: List[str]) -> Dict[str, int]:
    """Tạo mapping từ category name sang index"""
    return {cat: idx for idx, cat in enumerate(sorted(set(categories)))}

def load_improved_loss(tokenizer, categories: List[str], device='cuda'):
    """Factory function để tạo improved loss"""
    category_mapping = create_category_mapping(categories)
    
    # Base multi-task loss
    base_loss = ImprovedVLMLoss(tokenizer, category_mapping, device)
    
    # Wrap với curriculum learning
    curriculum_loss = CurriculumLoss(
        base_loss.multi_task_loss, 
        difficulty_schedule='exponential'
    )
    
    return curriculum_loss, base_loss
