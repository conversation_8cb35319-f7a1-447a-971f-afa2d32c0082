#!/usr/bin/env python3
"""
Test script để verify server training stability tr<PERSON><PERSON><PERSON> khi chạy production
- Test FixedBalancedBatchSampler finite epochs
- Test memory management
- Test checkpoint consistency
- Test error recovery
"""

import torch
import pandas as pd
import numpy as np
import logging
import time
import gc
from pathlib import Path
import tempfile
import shutil
from collections import defaultdict

# Import từ main training script
from Finetune_L4_Progressive_method import (
    FixedBalancedBatchSampler, 
    CategoryBalancedDataset,
    balanced_collate_fn,
    enhanced_compute_loss,
    ServerL4ProgressiveTrainer
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_dataset(num_samples=1000, num_categories=50):
    """Tạo test dataset nhỏ"""
    categories = [f"category_{i}" for i in range(num_categories)]
    
    data = []
    for i in range(num_samples):
        data.append({
            'food_name': f"food_{i}",
            'final_ultimate_category': np.random.choice(categories),
            'URL': f"https://example.com/image_{i}.jpg",
            'normalized_name': f"food_{i}".lower()
        })
    
    return pd.DataFrame(data)

def test_fixed_batch_sampler():
    """Test FixedBalancedBatchSampler finite epochs"""
    logger.info("Testing FixedBalancedBatchSampler...")
    
    # Create mock dataset
    df = create_test_dataset(500, 20)
    
    # Mock tokenizer
    class MockTokenizer:
        def __init__(self):
            self.eos_token = "</s>"
            self.pad_token = "</s>"
    
    tokenizer = MockTokenizer()
    
    # Create dataset
    dataset = CategoryBalancedDataset(df, tokenizer, difficulty_level=1, is_training=True)
    
    # Test sampler
    batch_size = 8
    max_batches = 50
    sampler = FixedBalancedBatchSampler(dataset, batch_size, shuffle=True, max_batches_per_epoch=max_batches)
    
    # Test finite epochs
    batches_generated = 0
    for batch in sampler:
        batches_generated += 1
        assert len(batch) <= batch_size, f"Batch size exceeded: {len(batch)} > {batch_size}"
        
        if batches_generated > max_batches + 10:  # Safety check
            raise AssertionError(f"Sampler generated too many batches: {batches_generated} > {max_batches}")
    
    assert batches_generated == max_batches, f"Expected {max_batches} batches, got {batches_generated}"
    logger.info(f"✅ FixedBalancedBatchSampler test passed: {batches_generated} batches generated")

def test_memory_management():
    """Test memory management và cleanup"""
    logger.info("Testing memory management...")
    
    if not torch.cuda.is_available():
        logger.info("⚠️ CUDA not available, skipping memory test")
        return
    
    initial_memory = torch.cuda.memory_allocated()
    
    # Create large tensors
    tensors = []
    for i in range(10):
        tensor = torch.randn(100, 3, 448, 448, device='cuda', dtype=torch.bfloat16)
        tensors.append(tensor)
    
    peak_memory = torch.cuda.memory_allocated()
    
    # Cleanup
    del tensors
    torch.cuda.empty_cache()
    gc.collect()
    
    final_memory = torch.cuda.memory_allocated()
    
    memory_cleaned = (peak_memory - final_memory) / (1024**2)  # MB
    logger.info(f"✅ Memory management test: {memory_cleaned:.1f}MB cleaned")
    
    assert final_memory <= initial_memory + 50*1024*1024, "Memory leak detected"

def test_checkpoint_consistency():
    """Test checkpoint save/load consistency"""
    logger.info("Testing checkpoint consistency...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create trainer
        trainer = ServerL4ProgressiveTrainer(output_dir=temp_dir)
        
        # Mock optimizer và scheduler
        optimizer = torch.optim.AdamW([torch.randn(10, requires_grad=True)], lr=1e-5)
        scheduler = torch.optim.lr_scheduler.ConstantLR(optimizer, factor=1.0)
        
        # Save checkpoint
        checkpoint_path = trainer.save_checkpoint(
            epoch=1, level=1, model=trainer.model, optimizer=optimizer, 
            scheduler=scheduler, train_losses=[0.5], val_losses=[0.4], best_loss=0.4
        )
        
        # Load checkpoint
        loaded_checkpoint = trainer.load_checkpoint(checkpoint_path)
        
        assert loaded_checkpoint is not None, "Failed to load checkpoint"
        assert loaded_checkpoint['epoch'] == 1, "Epoch mismatch"
        assert loaded_checkpoint['level'] == 1, "Level mismatch"
        assert loaded_checkpoint['best_loss'] == 0.4, "Best loss mismatch"
        
        logger.info("✅ Checkpoint consistency test passed")

def test_error_recovery():
    """Test error recovery mechanisms"""
    logger.info("Testing error recovery...")
    
    # Test URL validation
    trainer = ServerL4ProgressiveTrainer()
    
    # Test với invalid URLs
    invalid_urls = [
        "https://invalid-domain-12345.com/image.jpg",
        "not-a-url",
        "https://httpstat.us/404"
    ]
    
    valid_count = trainer._validate_sample_urls(invalid_urls)
    assert valid_count == 0, f"Expected 0 valid URLs, got {valid_count}"
    
    logger.info("✅ Error recovery test passed")

def test_dataset_loading():
    """Test dataset loading với different scenarios"""
    logger.info("Testing dataset loading scenarios...")
    
    # Test với missing columns
    df_missing = pd.DataFrame({
        'food_name': ['test'],
        'URL': ['https://example.com/test.jpg']
        # Missing 'final_ultimate_category'
    })
    
    try:
        trainer = ServerL4ProgressiveTrainer()
        # This should handle missing columns gracefully
        logger.info("✅ Missing column handling test passed")
    except Exception as e:
        logger.info(f"✅ Expected error for missing columns: {e}")

def test_batch_diversity():
    """Test category diversity trong batches"""
    logger.info("Testing batch diversity...")
    
    df = create_test_dataset(200, 10)  # 10 categories
    
    class MockTokenizer:
        def __init__(self):
            self.eos_token = "</s>"
            self.pad_token = "</s>"
    
    dataset = CategoryBalancedDataset(df, MockTokenizer(), difficulty_level=1)
    sampler = FixedBalancedBatchSampler(dataset, batch_size=8, max_batches_per_epoch=20)
    
    from torch.utils.data import DataLoader
    loader = DataLoader(dataset, batch_sampler=sampler, collate_fn=balanced_collate_fn)
    
    diversities = []
    for batch in loader:
        unique_categories = len(set(batch['categories']))
        diversities.append(unique_categories)
    
    avg_diversity = np.mean(diversities)
    logger.info(f"✅ Average batch diversity: {avg_diversity:.2f} categories per batch")
    
    assert avg_diversity > 1, "Batches should have multiple categories"

def run_all_tests():
    """Chạy tất cả tests"""
    logger.info("="*60)
    logger.info("RUNNING SERVER STABILITY TESTS")
    logger.info("="*60)
    
    tests = [
        test_fixed_batch_sampler,
        test_memory_management,
        test_checkpoint_consistency,
        test_error_recovery,
        test_dataset_loading,
        test_batch_diversity
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            logger.error(f"❌ {test_func.__name__} FAILED: {e}")
            failed += 1
    
    logger.info("="*60)
    logger.info(f"TEST RESULTS: {passed} passed, {failed} failed")
    logger.info("="*60)
    
    if failed == 0:
        logger.info("🎉 ALL TESTS PASSED - Server code is ready for production!")
    else:
        logger.error("⚠️ Some tests failed - Please fix before running production")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
