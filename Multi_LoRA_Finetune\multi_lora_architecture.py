#!/usr/bin/env python3
"""
Multi-LoRA Architecture cho Multi-Task VLM (Food + Harmful Detection)
- Task-specific LoRA adapters với rank 32+
- Shared backbone + specialized heads
- Efficient parameter sharing
- Dynamic task routing
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer
from peft import LoraConfig, get_peft_model, TaskType
import logging
from typing import Dict, List, Optional, Tuple
import math

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiLoRAConfig:
    """Configuration cho Multi-LoRA setup"""
    
    def __init__(self, 
                 base_rank=32,
                 food_rank=64,      # Higher rank cho food task (more complex)
                 harmful_rank=32,   # Lower rank cho harmful task (binary)
                 alpha_ratio=2.0,   # alpha = rank * alpha_ratio
                 dropout=0.1,
                 target_modules=None):
        
        self.base_rank = base_rank
        self.food_rank = food_rank
        self.harmful_rank = harmful_rank
        self.alpha_ratio = alpha_ratio
        self.dropout = dropout
        
        # Target modules cho Vintern VLM
        self.target_modules = target_modules or [
            "q_proj", "k_proj", "v_proj", "o_proj",  # Attention layers
            "gate_proj", "up_proj", "down_proj",     # MLP layers
            "embed_tokens", "lm_head"                # Embedding layers
        ]
    
    def get_food_config(self):
        """LoRA config cho food classification task"""
        return LoraConfig(
            r=self.food_rank,
            lora_alpha=int(self.food_rank * self.alpha_ratio),
            target_modules=self.target_modules,
            lora_dropout=self.dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False
        )
    
    def get_harmful_config(self):
        """LoRA config cho harmful detection task"""
        return LoraConfig(
            r=self.harmful_rank,
            lora_alpha=int(self.harmful_rank * self.alpha_ratio),
            target_modules=self.target_modules,
            lora_dropout=self.dropout,
            bias="none",
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False
        )

class TaskSpecificHead(nn.Module):
    """Task-specific classification head"""
    
    def __init__(self, hidden_size, num_classes, task_name, dropout=0.1):
        super().__init__()
        self.task_name = task_name
        self.num_classes = num_classes
        
        # Multi-layer head cho better representation
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LayerNorm(hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.LayerNorm(hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 4, num_classes)
        )
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights với Xavier uniform"""
        for module in self.classifier:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, hidden_states):
        """Forward pass"""
        return self.classifier(hidden_states)

class MultiLoRAVLM(nn.Module):
    """Multi-LoRA VLM cho multi-task learning"""
    
    def __init__(self, model_path, multi_lora_config, food_categories, device='cuda'):
        super().__init__()
        self.device = device
        self.multi_lora_config = multi_lora_config
        
        # Load base model
        logger.info(f"Loading base model: {model_path}")
        self.base_model = AutoModel.from_pretrained(
            model_path,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16,
            device_map="auto" if torch.cuda.is_available() else None
        )
        
        # Get hidden size
        self.hidden_size = self.base_model.config.hidden_size
        
        # Create task-specific LoRA adapters
        self.food_lora = self._create_lora_adapter("food", multi_lora_config.get_food_config())
        self.harmful_lora = self._create_lora_adapter("harmful", multi_lora_config.get_harmful_config())
        
        # Task-specific classification heads
        self.food_head = TaskSpecificHead(
            self.hidden_size, 
            len(food_categories), 
            "food_classification"
        ).to(device)
        
        self.harmful_head = TaskSpecificHead(
            self.hidden_size, 
            2,  # Binary: safe/harmful
            "harmful_detection"
        ).to(device)
        
        # Task routing
        self.task_router = nn.Linear(self.hidden_size, 2).to(device)  # 2 tasks
        
        # Category mappings
        self.food_categories = food_categories
        self.food_category_to_id = {cat: idx for idx, cat in enumerate(food_categories)}
        self.harmful_categories = ['safe', 'harmful']
        
        logger.info(f"Multi-LoRA VLM initialized:")
        logger.info(f"  Food categories: {len(food_categories)}")
        logger.info(f"  Food LoRA rank: {multi_lora_config.food_rank}")
        logger.info(f"  Harmful LoRA rank: {multi_lora_config.harmful_rank}")
        logger.info(f"  Hidden size: {self.hidden_size}")
    
    def _create_lora_adapter(self, task_name, lora_config):
        """Create LoRA adapter cho specific task"""
        logger.info(f"Creating LoRA adapter for {task_name} task (rank={lora_config.r})")
        
        # Clone base model cho task-specific adaptation
        task_model = AutoModel.from_pretrained(
            self.base_model.config.name_or_path,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16
        )
        
        # Apply LoRA
        lora_model = get_peft_model(task_model, lora_config)
        
        return lora_model.to(self.device)
    
    def get_visual_features(self, images, task_type):
        """Extract visual features với task-specific LoRA"""
        batch_size = images.size(0)
        
        # Route to appropriate LoRA adapter
        if task_type == 'food_classification':
            lora_model = self.food_lora
        else:
            lora_model = self.harmful_lora
        
        # Extract visual features
        with torch.cuda.amp.autocast():
            # Get image embeddings từ vision encoder
            vision_outputs = lora_model.vision_model(images)
            
            # Pool features (mean pooling)
            visual_features = vision_outputs.last_hidden_state.mean(dim=1)  # [B, hidden_size]
        
        return visual_features
    
    def forward(self, images, task_types, labels=None, return_features=False):
        """Forward pass với dynamic task routing"""
        batch_size = images.size(0)
        
        # Group by task type cho efficient processing
        food_indices = [i for i, task in enumerate(task_types) if task == 'food_classification']
        harmful_indices = [i for i, task in enumerate(task_types) if task == 'harmful_detection']
        
        all_logits = torch.zeros(batch_size, max(len(self.food_categories), 2), device=self.device)
        all_features = torch.zeros(batch_size, self.hidden_size, device=self.device)
        
        # Process food samples
        if food_indices:
            food_images = images[food_indices]
            food_features = self.get_visual_features(food_images, 'food_classification')
            food_logits = self.food_head(food_features)
            
            for i, idx in enumerate(food_indices):
                all_logits[idx, :len(self.food_categories)] = food_logits[i]
                all_features[idx] = food_features[i]
        
        # Process harmful samples
        if harmful_indices:
            harmful_images = images[harmful_indices]
            harmful_features = self.get_visual_features(harmful_images, 'harmful_detection')
            harmful_logits = self.harmful_head(harmful_features)
            
            for i, idx in enumerate(harmful_indices):
                all_logits[idx, :2] = harmful_logits[i]
                all_features[idx] = harmful_features[i]
        
        if return_features:
            return all_logits, all_features
        
        return all_logits
    
    def predict(self, images, task_types):
        """Prediction với confidence scores"""
        self.eval()
        
        with torch.no_grad():
            logits = self.forward(images, task_types)
            
            predictions = []
            confidences = []
            
            for i, task_type in enumerate(task_types):
                if task_type == 'food_classification':
                    task_logits = logits[i, :len(self.food_categories)]
                    probs = F.softmax(task_logits, dim=0)
                    pred_idx = torch.argmax(probs)
                    confidence = probs[pred_idx].item()
                    prediction = self.food_categories[pred_idx]
                else:
                    task_logits = logits[i, :2]
                    probs = F.softmax(task_logits, dim=0)
                    pred_idx = torch.argmax(probs)
                    confidence = probs[pred_idx].item()
                    prediction = self.harmful_categories[pred_idx]
                
                predictions.append(prediction)
                confidences.append(confidence)
        
        return predictions, confidences
    
    def get_trainable_parameters(self):
        """Get trainable parameters count"""
        total_params = 0
        trainable_params = 0
        
        # Base model parameters (frozen)
        for param in self.base_model.parameters():
            total_params += param.numel()
        
        # LoRA parameters (trainable)
        for param in self.food_lora.parameters():
            if param.requires_grad:
                trainable_params += param.numel()
        
        for param in self.harmful_lora.parameters():
            if param.requires_grad:
                trainable_params += param.numel()
        
        # Classification heads (trainable)
        for param in self.food_head.parameters():
            trainable_params += param.numel()
            total_params += param.numel()
        
        for param in self.harmful_head.parameters():
            trainable_params += param.numel()
            total_params += param.numel()
        
        for param in self.task_router.parameters():
            trainable_params += param.numel()
            total_params += param.numel()
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'trainable_percentage': trainable_params / total_params * 100
        }
    
    def save_lora_adapters(self, output_dir):
        """Save LoRA adapters"""
        from pathlib import Path
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Save food LoRA
        food_dir = output_dir / "food_lora"
        self.food_lora.save_pretrained(food_dir)
        
        # Save harmful LoRA
        harmful_dir = output_dir / "harmful_lora"
        self.harmful_lora.save_pretrained(harmful_dir)
        
        # Save classification heads
        torch.save(self.food_head.state_dict(), output_dir / "food_head.pt")
        torch.save(self.harmful_head.state_dict(), output_dir / "harmful_head.pt")
        torch.save(self.task_router.state_dict(), output_dir / "task_router.pt")
        
        # Save metadata
        metadata = {
            'food_categories': self.food_categories,
            'harmful_categories': self.harmful_categories,
            'hidden_size': self.hidden_size,
            'multi_lora_config': {
                'food_rank': self.multi_lora_config.food_rank,
                'harmful_rank': self.multi_lora_config.harmful_rank,
                'alpha_ratio': self.multi_lora_config.alpha_ratio
            }
        }
        
        import json
        with open(output_dir / "model_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Multi-LoRA model saved to {output_dir}")
    
    def load_lora_adapters(self, model_dir):
        """Load LoRA adapters"""
        from pathlib import Path
        model_dir = Path(model_dir)
        
        # Load LoRA adapters
        food_dir = model_dir / "food_lora"
        harmful_dir = model_dir / "harmful_lora"
        
        if food_dir.exists():
            self.food_lora.load_adapter(food_dir, "food")
        
        if harmful_dir.exists():
            self.harmful_lora.load_adapter(harmful_dir, "harmful")
        
        # Load classification heads
        if (model_dir / "food_head.pt").exists():
            self.food_head.load_state_dict(torch.load(model_dir / "food_head.pt"))
        
        if (model_dir / "harmful_head.pt").exists():
            self.harmful_head.load_state_dict(torch.load(model_dir / "harmful_head.pt"))
        
        if (model_dir / "task_router.pt").exists():
            self.task_router.load_state_dict(torch.load(model_dir / "task_router.pt"))
        
        logger.info(f"Multi-LoRA model loaded from {model_dir}")

def create_multi_lora_model(model_path, food_categories, 
                           food_rank=64, harmful_rank=32, device='cuda'):
    """Factory function để tạo Multi-LoRA model"""
    
    # Create config
    config = MultiLoRAConfig(
        food_rank=food_rank,
        harmful_rank=harmful_rank,
        alpha_ratio=2.0,
        dropout=0.1
    )
    
    # Create model
    model = MultiLoRAVLM(model_path, config, food_categories, device)
    
    # Log parameter info
    param_info = model.get_trainable_parameters()
    logger.info(f"Model parameter info:")
    logger.info(f"  Total parameters: {param_info['total_parameters']:,}")
    logger.info(f"  Trainable parameters: {param_info['trainable_parameters']:,}")
    logger.info(f"  Trainable percentage: {param_info['trainable_percentage']:.2f}%")
    
    return model

class MultiTaskLoss(nn.Module):
    """Multi-task loss với task balancing"""

    def __init__(self, food_weight=1.0, harmful_weight=1.0,
                 focal_gamma=2.0, label_smoothing=0.1):
        super().__init__()
        self.food_weight = food_weight
        self.harmful_weight = harmful_weight
        self.focal_gamma = focal_gamma

        # Loss functions
        self.food_criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        self.harmful_criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)

    def focal_loss(self, logits, targets, gamma=2.0):
        """Focal loss cho imbalanced data"""
        ce_loss = F.cross_entropy(logits, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** gamma * ce_loss
        return focal_loss.mean()

    def forward(self, logits, labels, task_types, food_categories):
        """Compute multi-task loss"""
        total_loss = 0.0
        task_losses = {}

        # Separate by task
        food_indices = [i for i, task in enumerate(task_types) if task == 'food_classification']
        harmful_indices = [i for i, task in enumerate(task_types) if task == 'harmful_detection']

        # Food classification loss
        if food_indices:
            food_logits = logits[food_indices, :len(food_categories)]
            food_labels = torch.tensor([
                food_categories.index(labels[i]) for i in food_indices
            ], device=logits.device)

            food_loss = self.focal_loss(food_logits, food_labels, self.focal_gamma)
            total_loss += self.food_weight * food_loss
            task_losses['food'] = food_loss.item()

        # Harmful detection loss
        if harmful_indices:
            harmful_logits = logits[harmful_indices, :2]
            harmful_labels = torch.tensor([
                0 if labels[i] == 'safe' else 1 for i in harmful_indices
            ], device=logits.device)

            harmful_loss = self.focal_loss(harmful_logits, harmful_labels, self.focal_gamma)
            total_loss += self.harmful_weight * harmful_loss
            task_losses['harmful'] = harmful_loss.item()

        return total_loss, task_losses
