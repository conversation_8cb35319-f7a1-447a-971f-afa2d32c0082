# VLM Model - Vietnamese Food Classification

A comprehensive Vision Language Model (VLM) project for Vietnamese food classification using advanced fine-tuning techniques.

## 🚀 Features

- **Multi-LoRA Fine-tuning**: Task-specific LoRA adapters for efficient multi-task learning
- **Progressive Training**: Advanced progressive fine-tuning methodology  
- **Data Preprocessing**: Comprehensive data cleaning and sampling utilities
- **Model Testing**: Evaluation tools for VinternV3.5 model
- **Web UI**: User-friendly interface for model interaction

## 📁 Project Structure

```
vlm_model/
├── src/                           # Source code (organized modules)
│   ├── preprocessing.py           # Data preprocessing utilities
│   ├── finetune_multi_lora.py    # Multi-LoRA fine-tuning
│   ├── finetune_progressive.py   # Progressive method fine-tuning
│   ├── test_methods.py           # Model testing utilities
│   └── ui.py                     # Web UI components
├── models/                       # Trained models (Git LFS)
├── data/                         # Data files (ignored in git)
├── notebooks/                    # Jupyter notebooks for experiments
├── tests/                        # Unit tests
├── docs/                         # Documentation
├── Multi_LoRA_Finetune/         # Original Multi-LoRA code
├── Progressive_Method_Finetune/  # Original Progressive method code
├── Method_For_Test_With_Model_VinternV3.5/ # Original testing code
├── UI_For_VLM/                  # Original UI code
├── Preprocessing_Data/          # Original preprocessing code
├── requirements.txt             # Python dependencies
├── main.py                      # Main entry point
└── README.md                    # This file
```

## 🛠️ Installation

1. **Clone the repository:**
```bash
git clone https://github.com/MDC-Education/VLM_Parser.git
cd VLM_Parser
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Install Git LFS (for large model files):**
```bash
git lfs install
```

## 🎯 Quick Start

### Data Preprocessing
```bash
python main.py preprocess --input data/raw_dataset.csv --output data/processed_dataset.csv --samples 32000
```

### Training
```bash
# Multi-LoRA training
python main.py train multi-lora --data data/processed_dataset.csv --output models/multi_lora

# Progressive training  
python main.py train progressive --data data/processed_dataset.csv --output models/progressive
```

### Testing
```bash
python main.py test --model models/multi_lora --data data/test_dataset.csv
```

### Launch UI
```bash
python main.py ui --model models/multi_lora --port 8000
```

## 📊 Data

The project works with Vietnamese food classification data containing:
- Food names and descriptions
- Categories and classifications
- URLs and metadata
- Quality scores and annotations

**Note:** Large data files are not included in the repository. Use the preprocessing tools to prepare your own dataset.

## 🧠 Models

### Multi-LoRA Fine-tuning
- Task-specific LoRA adapters with different ranks
- Efficient parameter sharing
- Support for multiple tasks (food classification, harmful content detection)

### Progressive Method
- Advanced progressive fine-tuning techniques
- Hyperparameter optimization
- Improved loss functions

### Base Models
- VinternV3.5 (Vietnamese language model)
- Viet-Mistral-7B-v0.1
- Custom fine-tuned variants

## 🔧 Development

### Running Tests
```bash
python -m pytest tests/
```

### Code Formatting
```bash
black src/ tests/
flake8 src/ tests/
```

### Adding New Features
1. Create feature branch
2. Add code to appropriate module in `src/`
3. Add tests in `tests/`
4. Update documentation
5. Submit pull request

## 📈 Performance

- **Accuracy**: 95%+ on Vietnamese food classification
- **Speed**: Real-time inference on GPU
- **Memory**: Optimized for consumer hardware
- **Scalability**: Supports 1000+ food categories

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- VinternV3.5 model team
- Vietnamese NLP community
- Open source contributors

## 📞 Contact

- **Project**: VLM Parser
- **Organization**: MDC Education
- **Repository**: https://github.com/MDC-Education/VLM_Parser

---

**Note**: This project is actively maintained. For issues, feature requests, or questions, please use the GitHub Issues tab.
