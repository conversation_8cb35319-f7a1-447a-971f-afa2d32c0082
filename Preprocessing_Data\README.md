## Tổng quan pipeline tiền xử lý dữ liệu (Hybrid: Rules-based + AI 2 rounds)

Mục tiêu: <PERSON><PERSON><PERSON> hóa, phân loại và hợp nhất toàn bộ dữ liệu món ăn Việt Nam với độ phủ và chất lượng cao để sẵn sàng fine-tune VLM.

- Tổng số item: 805,508
- Tỉ lệ được phân loại cuối cùng: 92.4% (từ 66.7% → 92.4%)
- Tổng số category cuối: 1,415 (38 keyword + 1,377 AI)
- <PERSON><PERSON><PERSON>n sự thật số liệu: Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_ANALYSIS.json, FINAL_ULTIMATE_COMPLETE_REPORT.md

### Luồng xử lý tổng
1) Nạp & chuẩn hóa dữ liệu (chunk-based, normalize tiếng Việt, xử lý edge cases)
2) Phân loại Rules-based (từ khóa/phrase, ưu tiên ngữ nghĩa, combo detection)
3) AI Round 1: PhoBERT + TF-IDF + DBSCAN → gợi ý cluster/chuyên mục mới
4) AI Round 2: Nâng cấp đặc trưng + DBSCAN → mở rộng/hoàn thiện phân loại khó
5) Hợp nhất nhiều tầng: Gộp kết quả Keyword + AI R1 + AI R2 với theo dõi nguồn

Kết quả được ghi nhận rõ ràng theo “classification_method”: "Keyword" | "AI" | "AI Round 2".

---

## 1) Thiết kế Rules-based (Round 1)
Thực thi ở thư mục: All_Data_After_Preprocessing/ConcatResultAfterEachRound/Round1_RuleBase

Tư tưởng chính:
- Match theo phrase (không chỉ single-word) với scoring coverage
- Chuẩn hóa tiếng Việt (có/không dấu), xử lý biến thể vùng miền
- Thứ tự ưu tiên: Combo > Món cụ thể > Nhóm nguyên liệu/kiểu chế biến > Mẫu tổng quát
- Phát hiện combo (phở + bò; trà sữa + topping…) và gán đúng category combo
- Fix 9 edge cases lớn (được mô tả trong FINAL_ULTIMATE_COMPLETE_REPORT.md)

Đầu vào/Đầu ra điển hình:
- Input: dataset gốc (CSV/JSON sau normalize)
- Output Round1: FINAL_Classify_RuleBase_DATASET.csv + UNCLASSIFIED_ITEMS_FOR_AI.csv (những item chưa phân loại để chuyển cho AI)

Tài liệu tham chiếu:
- ROUND2_COMPLETE_CLASS_LABELS_EXPORT.md mô tả bộ nhãn chi tiết (98+ nhãn base) & cách match phrase

---

## 2) AI Round 1 (PhoBERT + TF-IDF + DBSCAN)
Mục tiêu: Nâng phủ phân loại cho phần chưa match được bằng rule — tạo thêm chuyên mục (cluster) hợp lý.

Thiết kế đặc trưng & mô hình:
- PhoBERT embeddings (768-d) để hiểu ngữ nghĩa tên món/chuỗi
- TF-IDF (≈1000-d) nắm bắt tầm quan trọng từ khóa/phrase
- Gộp đặc trưng (semantic + lexical) → DBSCAN để phát hiện cluster tự nhiên
- Đặt tên cluster thông minh dựa trên pattern/keywords xuất hiện mạnh

Kết quả (theo báo cáo):
- Sinh 705 cluster
- Phân loại thêm: +127,442 items (~+15.8%)

Đầu ra: “AI” trong classification_method, file hợp nhất trung gian: FINAL_ULTIMATE_CLASSIFIED_DATASET.csv (Keyword + AI R1)

---

## 3) AI Round 2 (Nâng cấp đặc trưng + DBSCAN)
Mục tiêu: Tăng thêm độ phủ ở các trường hợp khó/biên.

Nâng cấp:
- PhoBERT embeddings tối ưu batch
- TF-IDF mở rộng (≈1500-d, dùng trigram)
- DBSCAN tinh chỉnh để tách/nhập cụm tốt hơn
- Scoring cải tiến khi đặt tên/ghép vào category thích hợp

Kết quả (theo báo cáo):
- Sinh 1,153 cluster
- Phân loại thêm: +79,745 items (~+9.9%)

Đầu ra: mapping bổ sung Round 2 (các item được điền "AI Round 2"), rồi chuyển sang bước hợp nhất cuối.

---

## 4) Hợp nhất đa tầng (Final Ultimate Combination)
Script: All_Data_After_Preprocessing/ConcatResultAfterEachRound/final_ultimate_combination.py

Chức năng:
- Nạp FINAL_ULTIMATE_CLASSIFIED_DATASET.csv (Keyword + AI R1)
- Nạp kết quả AI R2 (FINAL_AI_ROUND2_CLASSIFIED.csv)
- Với các item còn "Chưa phân loại" ở base, nếu có mapping R2 → gán final_ultimate_category và classification_method="AI Round 2"
- Xuất:
  - FINAL_ULTIMATE_COMPLETE_DATASET.csv (các cột chính: food_name, final_ultimate_category, normalized_name, is_combo, classification_method, URL)
  - FINAL_ULTIMATE_COMPLETE_ANALYSIS.json (thống kê & phân rã theo phương pháp)

Số liệu then chốt (từ ANALYSIS.json):
- keyword_classified: 598,321 (~74.3%)
- ai_round1_classified: 127,442 (+15.8%)
- ai_round2_classified: 79,745 (+9.9%)
- final_classified: 744,415 / 805,508 (92.4%)
- still_unclassified: 61,093
- total_categories: 1,415

---

## 5) Phân tích, thống kê và biểu đồ gợi ý
Nguồn số liệu: FINAL_ULTIMATE_COMPLETE_ANALYSIS.json

Bạn có thể dựng nhanh các biểu đồ sau bằng Python (matplotlib/seaborn):

- Biểu đồ stacked bar tỉ lệ phân loại theo phương pháp (Keyword vs AI R1 vs AI R2 vs Unclassified)
- Top N chuyên mục lớn nhất (bar chart)
- Phổ phân bố số mẫu/category (histogram) để xem độ lệch phân phối

Ví dụ code nhanh (đọc ANALYSIS.json và vẽ 2 biểu đồ):

```python
import json, matplotlib.pyplot as plt
from pathlib import Path

stats = json.loads(Path('Preprocessing_Data/All_Data_After_Preprocessing/FINAL_ULTIMATE_COMPLETE_ANALYSIS.json').read_text(encoding='utf-8'))

# 1) Breakdown theo phương pháp
methods = ['Keyword', 'AI Round 1', 'AI Round 2', 'Unclassified']
values = [
    stats['method_distribution']['Keyword'],
    stats['method_distribution']['AI Round 1'],
    stats['method_distribution']['AI Round 2'],
    stats['classification_breakdown']['still_unclassified'],
]
plt.figure(figsize=(6,4))
plt.bar(methods, values, color=['#6baed6','#9ecae1','#c6dbef','#fdd0a2'])
plt.title('Breakdown số lượng theo phương pháp phân loại')
plt.ylabel('Số mẫu')
plt.xticks(rotation=15)
plt.tight_layout(); plt.show()

# 2) Top 20 category lớn nhất
top = list(stats['top_categories'].items())[:20]
labels = [k for k, _ in top]
counts = [v for _, v in top]
plt.figure(figsize=(8,6))
plt.barh(labels[::-1], counts[::-1], color='#74c476')
plt.title('Top 20 category theo số mẫu')
plt.xlabel('Số mẫu')
plt.tight_layout(); plt.show()
```

Gợi ý thêm:
- Vẽ đường tiến triển tỉ lệ phân loại qua các giai đoạn: Keyword → +AI R1 → +AI R2
- Heatmap tương quan giữa cluster AI và nhãn keyword gốc (để kiểm tra hòa hợp ngữ nghĩa)

---

## 6) Hướng dẫn chạy nhanh

A) Hợp nhất kết quả Final Ultimate
- Điều kiện: Có sẵn FINAL_ULTIMATE_CLASSIFIED_DATASET.csv và FINAL_AI_ROUND2_CLASSIFIED.csv trong thư mục ConcatResultAfterEachRound/
- Chạy:
  - cd Preprocessing_Data/All_Data_After_Preprocessing/ConcatResultAfterEachRound
  - python final_ultimate_combination.py
- Kết quả: FINAL_ULTIMATE_COMPLETE_DATASET.csv + FINAL_ULTIMATE_COMPLETE_ANALYSIS.json

B) Chuẩn bị dữ liệu cân bằng 25 mẫu/category cho huấn luyện local
- Script: Analyze_Prepare_For_Train/analyze_and_prepare_training_data.py
- Thực hiện:
  - cd Preprocessing_Data/All_Data_After_Preprocessing/Analyze_Prepare_For_Train
  - python analyze_and_prepare_training_data.py  (mặc định 25 mẫu/category, min 10)
- Kết quả: thư mục vintern_training_data/ gồm training_data.json, balanced_training_dataset.csv, category_mapping.json, training_statistics.json

C) Lấy mẫu 32k để fine-tune nhanh (tùy chọn)
- Script: Take_A_Sample_To_Run_Local/Final_32k_Sampler.py
- Chạy: cd Preprocessing_Data/Take_A_Sample_To_Run_Local && python Final_32k_Sampler.py
- Kết quả: Final_32k_Dataset.csv + báo cáo

---

## 7) Rủi ro tiềm tàng & cải tiến đề xuất

Rủi ro/điểm chú ý:
- Drift/Leak: AI clustering có thể đặt tên cụm chưa tối ưu → cần human-in-the-loop cho top-cụm lớn
- Ambiguity: Tên món ngắn/đa nghĩa ("cơm", "bánh") → nên dùng thêm ngữ cảnh ảnh khi gộp kết quả với VLM
- Imbalance: Phân phối category lệch rất mạnh → ảnh hưởng fine-tuning nếu không cân bằng/weighting tốt
- URL hỏng/hết hạn: Ảnh không tải được ảnh hưởng chất lượng training/inference

Cải tiến:
- Thêm weak supervision/label model (Snorkel-style) để phối hợp nhiều heuristic
- Bổ sung validation bán tự động: active learning cho các cụm AI khó
- Chuẩn hóa label ontology nhiều tầng (siêu nhóm → nhóm → nhãn cụ thể)
- Gán confidence cho từng phương pháp và hợp nhất theo voting có trọng số
- Kiểm định chéo bằng mô hình ngôn ngữ/ảnh (cross-modal agreement) trước khi chốt nhãn

---

## 8) Liên kết tài liệu quan trọng
- FINAL_ULTIMATE_COMPLETE_REPORT.md: Báo cáo chi tiết toàn hệ thống
- ConcatResultAfterEachRound/Round2_AI_ForBasicKeyWord/ROUND2_COMPLETE_CLASS_LABELS_EXPORT.md: Danh mục nhãn & đặc tả kỹ thuật rule
- final_ultimate_combination.py: Script hợp nhất cuối
- FINAL_ULTIMATE_COMPLETE_ANALYSIS.json: Số liệu tổng hợp sau hợp nhất
- Analyze_Prepare_For_Train/analyze_and_prepare_training_data.py: Chuẩn bị dữ liệu train cân bằng
- Take_A_Sample_To_Run_Local/Final_32k_Sampler.py: Lấy mẫu 32k cho huấn luyện nhanh
