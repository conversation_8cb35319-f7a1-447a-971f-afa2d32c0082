#!/usr/bin/env python3
"""
Final Ultimate Combination - Merge all results: Keyword + AI Round 1 + AI Round 2
"""

import pandas as pd
import json
import logging
from pathlib import Path
from collections import Counter

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def final_ultimate_combination():
    """Combine all classification results for ultimate dataset"""
    logger.info("🚀 FINAL ULTIMATE COMBINATION")
    logger.info("="*60)
    
    try:
        # Load base dataset (keyword + AI round 1)
        base_file = "FINAL_ULTIMATE_CLASSIFIED_DATASET.csv"
        if not Path(base_file).exists():
            logger.error(f"❌ Base file not found: {base_file}")
            return
        
        base_df = pd.read_csv(base_file, encoding='utf-8')
        logger.info(f"📊 Loaded base results: {len(base_df):,} items")
        
        # Load AI Round 2 results
        round2_file = "FINAL_AI_ROUND2_CLASSIFIED.csv"
        if not Path(round2_file).exists():
            logger.error(f"❌ Round 2 file not found: {round2_file}")
            return
        
        round2_df = pd.read_csv(round2_file, encoding='utf-8')
        logger.info(f"📊 Loaded AI Round 2 results: {len(round2_df):,} items")
        
        # Create mapping from AI Round 2 results
        round2_mapping = {}
        for _, row in round2_df.iterrows():
            if row['ai_category_round2'] != 'Chưa phân loại':
                round2_mapping[row['food_name']] = row['ai_category_round2']
        
        logger.info(f"📊 AI Round 2 classified items: {len(round2_mapping):,}")
        
        # Apply AI Round 2 results to remaining unclassified items
        ultimate_df = base_df.copy()
        ultimate_df['final_ultimate_category'] = ultimate_df['final_combined_category']
        ultimate_df['classification_method'] = ultimate_df['classification_source']
        
        round2_applied_count = 0
        for idx, row in ultimate_df.iterrows():
            if row['final_combined_category'] == 'Chưa phân loại':
                if row['food_name'] in round2_mapping:
                    ultimate_df.loc[idx, 'final_ultimate_category'] = round2_mapping[row['food_name']]
                    ultimate_df.loc[idx, 'classification_method'] = 'AI Round 2'
                    round2_applied_count += 1
        
        logger.info(f"📊 AI Round 2 classifications applied: {round2_applied_count:,}")
        
        # Final ultimate analysis
        logger.info("\n📊 FINAL ULTIMATE RESULTS")
        logger.info("-" * 60)
        
        category_counts = ultimate_df['final_ultimate_category'].value_counts()
        classified_count = len(ultimate_df[ultimate_df['final_ultimate_category'] != 'Chưa phân loại'])
        classification_rate = (classified_count / len(ultimate_df)) * 100
        
        # Previous rates for comparison
        keyword_classified = len(ultimate_df[ultimate_df['classification_method'] == 'Keyword'])
        ai_round1_classified = len(ultimate_df[ultimate_df['classification_method'] == 'AI'])
        ai_round2_classified = len(ultimate_df[ultimate_df['classification_method'] == 'AI Round 2'])
        
        keyword_rate = (keyword_classified / len(ultimate_df)) * 100
        round1_improvement = (ai_round1_classified / len(ultimate_df)) * 100
        round2_improvement = (ai_round2_classified / len(ultimate_df)) * 100
        
        logger.info(f"Total items: {len(ultimate_df):,}")
        logger.info(f"Keyword classified: {keyword_classified:,} ({keyword_rate:.1f}%)")
        logger.info(f"AI Round 1 added: {ai_round1_classified:,} (+{round1_improvement:.1f}%)")
        logger.info(f"AI Round 2 added: {ai_round2_classified:,} (+{round2_improvement:.1f}%)")
        logger.info(f"Final classified: {classified_count:,} ({classification_rate:.1f}%)")
        logger.info(f"Total improvement: +{classification_rate - keyword_rate:.1f}%")
        logger.info(f"Total categories: {len(category_counts)}")
        
        # Show top categories
        logger.info(f"\n🏷️ TOP 30 FINAL ULTIMATE CATEGORIES:")
        for i, (category, count) in enumerate(category_counts.head(30).items(), 1):
            percentage = (count / len(ultimate_df)) * 100
            logger.info(f"  {i:2d}. {category}: {count:,} ({percentage:.1f}%)")
        
        # Save final ultimate results
        logger.info("\n💾 Saving final ultimate results...")
        
        output_file = "FINAL_ULTIMATE_COMPLETE_DATASET.csv"
        result_columns = ['food_name', 'final_ultimate_category', 'normalized_name', 'is_combo', 'classification_method']
        if 'URL' in ultimate_df.columns:
            result_columns.append('URL')
        elif 'image_link' in ultimate_df.columns:
            result_columns.append('image_link')
        
        ultimate_df[result_columns].to_csv(output_file, index=False, encoding='utf-8')
        
        # Save final ultimate analysis
        ultimate_analysis = {
            'total_items': int(len(ultimate_df)),
            'keyword_classified': int(keyword_classified),
            'keyword_classification_rate': float(keyword_rate),
            'ai_round1_classified': int(ai_round1_classified),
            'ai_round1_improvement': float(round1_improvement),
            'ai_round2_classified': int(ai_round2_classified),
            'ai_round2_improvement': float(round2_improvement),
            'final_classified': int(classified_count),
            'final_classification_rate': float(classification_rate),
            'total_ai_improvement': float(classification_rate - keyword_rate),
            'total_categories': int(len(category_counts)),
            'category_distribution': {str(k): int(v) for k, v in category_counts.to_dict().items()},
            'top_categories': {str(k): int(v) for k, v in category_counts.head(50).to_dict().items()},
            'classification_breakdown': {
                'keyword_only': int(keyword_classified),
                'ai_round1_enhanced': int(ai_round1_classified),
                'ai_round2_enhanced': int(ai_round2_classified),
                'still_unclassified': int(len(ultimate_df) - classified_count)
            },
            'method_distribution': {
                'Keyword': int(keyword_classified),
                'AI Round 1': int(ai_round1_classified),
                'AI Round 2': int(ai_round2_classified)
            }
        }
        
        with open("FINAL_ULTIMATE_COMPLETE_ANALYSIS.json", 'w', encoding='utf-8') as f:
            json.dump(ultimate_analysis, f, indent=2, ensure_ascii=False)
        
        # Create enhanced category summary
        category_summary = {}
        method_summary = {'Keyword': 0, 'AI Round 1': 0, 'AI Round 2': 0}
        
        for category, count in category_counts.items():
            if category == 'Chưa phân loại':
                main_cat = 'Chưa phân loại'
            else:
                main_cat = category.split(' / ')[0] if ' / ' in category else category
            
            if main_cat not in category_summary:
                category_summary[main_cat] = 0
            category_summary[main_cat] += count
        
        # Method summary
        method_counts = ultimate_df['classification_method'].value_counts()
        for method, count in method_counts.items():
            if method in method_summary:
                method_summary[method] = count
        
        logger.info(f"\n📊 FINAL ULTIMATE CATEGORY SUMMARY:")
        for main_cat, count in sorted(category_summary.items(), key=lambda x: x[1], reverse=True)[:20]:
            percentage = (count / len(ultimate_df)) * 100
            logger.info(f"  {main_cat}: {count:,} items ({percentage:.1f}%)")
        
        logger.info(f"\n🤖 CLASSIFICATION METHOD BREAKDOWN:")
        for method, count in method_summary.items():
            percentage = (count / len(ultimate_df)) * 100
            logger.info(f"  {method}: {count:,} items ({percentage:.1f}%)")
        
        logger.info(f"\n🎉 FINAL ULTIMATE COMBINATION COMPLETED!")
        logger.info(f"📁 Final dataset: {output_file}")
        logger.info(f"📊 Final classification rate: {classification_rate:.1f}%")
        logger.info(f"📈 Total AI improvement: +{classification_rate - keyword_rate:.1f}%")
        logger.info(f"🏷️ Total categories: {len(category_counts)}")
        
        return ultimate_df, ultimate_analysis
        
    except Exception as e:
        logger.error(f"❌ Final combination failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """Main function"""
    try:
        ultimate_df, analysis = final_ultimate_combination()
        
        if ultimate_df is not None:
            logger.info(f"\n🎉 ULTIMATE SUCCESS! FINAL COMPLETE DATASET CREATED!")
            logger.info(f"📊 Total items: {len(ultimate_df):,}")
            logger.info(f"📊 Final classification rate: {analysis['final_classification_rate']:.1f}%")
            logger.info(f"📈 Total AI improvement: +{analysis['total_ai_improvement']:.1f}%")
            logger.info(f"🏷️ Total categories: {analysis['total_categories']}")
            logger.info(f"🤖 AI Round 1: +{analysis['ai_round1_improvement']:.1f}%")
            logger.info(f"🤖 AI Round 2: +{analysis['ai_round2_improvement']:.1f}%")
            logger.info("📁 File: FINAL_ULTIMATE_COMPLETE_DATASET.csv")
        
    except Exception as e:
        logger.error(f"❌ Main process failed: {e}")

if __name__ == "__main__":
    main()
